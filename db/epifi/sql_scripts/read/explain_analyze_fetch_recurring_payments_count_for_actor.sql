EXPLAIN ANALYZE
SELECT state, count(*) AS count
FROM recurring_payments
WHERE state != 'CREATION_QUEUED'
  AND (
	from_actor_id = 'AC210826y2kM5kypRZyduy0Ts6isXg=='
		OR (
		to_actor_id = 'AC210826y2kM5kypRZyduy0Ts6isXg=='
			AND share_to_payee = true
			AND initiated_by = 'PAYER'
			AND state != 'CREATION_INITIATED'
			AND type = 'UPI_MANDATES'
		)
		OR (
		to_actor_id = 'AC210826y2kM5kypRZyduy0Ts6isXg=='
			AND share_to_payee = true
			AND initiated_by = 'PAYEE'
		)
	)
  AND recurring_payments.deleted_at IS NULL
GROUP BY state;

EXPLAIN ANALYZE
SELECT state, count(*) AS count
FROM recurring_payments
WHERE state != 'CREATION_QUEUED'
  AND (
	from_actor_id = 'AC221109/8olXeRMSDm7hQ7HgB4nBA=='
		OR (
		to_actor_id = 'AC221109/8olXeRMSDm7hQ7HgB4nBA=='
			AND share_to_payee = true
			AND initiated_by = 'PAYER'
			AND state != 'CREATION_INITIATED'
			AND type = 'UPI_MANDATES'
		)
		OR (
		to_actor_id = 'AC221109/8olXeRMSDm7hQ7HgB4nBA=='
			AND share_to_payee = true
			AND initiated_by = 'PAYEE'
		)
	)
  AND recurring_payments.deleted_at IS NULL
GROUP BY state;
