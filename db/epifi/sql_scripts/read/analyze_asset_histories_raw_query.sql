EXPLAIN ANALYZE
SELECT
    rh.actor_id,
    rh.asset_type,
    rh.history_date,
    rh.data,
    rh.id,
    rh.created_at,
    rh.updated_at
FROM (
         SELECT
             ah.*,  -- Select all columns from asset_histories
             ROW_NUMBER() OVER (PARTITION BY ah.actor_id, ah.asset_type ORDER BY ah.history_date DESC) as rn
         FROM
             asset_histories ah
         WHERE
             ah.actor_id = 'dummy-actor-id'
           AND ah.asset_type IN ('ASSET_TYPE_1', 'ASSET_TYPE_2')
           AND ah.history_date <= '2025-05-08'
     ) AS rh
WHERE
    rh.rn = 1;
