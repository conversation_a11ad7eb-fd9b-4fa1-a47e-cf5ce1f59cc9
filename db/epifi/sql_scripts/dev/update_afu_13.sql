-- https://monorail.pointz.in/p/fi-app/issues/detail?id=93053

--user 1
update auth_factor_updates
set overall_status = 'OVERALL_STATUS_IN_PROGRESS',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = '50285cc3-15e4-4e6e-953b-8d078efff160';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{requestId}', to_jsonb('DevReRegWed202502051152098vgrm'))
where id = '50285cc3-15e4-4e6e-953b-8d078efff160';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED'))
where id = '50285cc3-15e4-4e6e-953b-8d078efff160';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_UNSPECIFIED'))
where id = '50285cc3-15e4-4e6e-953b-8d078efff160';
