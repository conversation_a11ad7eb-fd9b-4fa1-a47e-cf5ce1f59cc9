-- update overallStatus as in progress which got marked as failure due to OBE0170 in enquiry
-- https://monorail.pointz.in/p/fi-app/issues/detail?id=80092

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_IN_PROGRESS',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = 'dd1b6a69-38b4-4638-907d-30619b4d7925';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED'))
where id = 'dd1b6a69-38b4-4638-907d-30619b4d7925';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_UNSPECIFIED'))
where id = 'dd1b6a69-38b4-4638-907d-30619b4d7925';
