-- Query to understand the performance implications of the all transactions query for user with ~200 transactions


-- Query with in-memory join
explain
analyze (distsql )WITH TXN AS (SELECT row_number() OVER ( PARTITION BY t.order_ref_id ORDER BY t.created_at DESC) AS rn,
				 id,
					updated_at,
					debited_at,
					credited_at,
					order_ref_id
			 FROM transactions t
			 WHERE (t.status = 'SUCCESS' AND ((t.pi_from IN ('PI220722IRAVUI0IR9maNMKimEYhYw==', 'PI220722VJRMtjGGQmi44cekD2PBYw==',
									'PI220722zHL+799rR+eC4wrIUgI6FQ==') AND
					  COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
					  '2023-02-22 10:31:33.328879' ::TIMESTAMPTZ) OR
					 (t.pi_to IN ('PI220722IRAVUI0IR9maNMKimEYhYw==', 'PI220722VJRMtjGGQmi44cekD2PBYw==',
									'PI220722zHL+799rR+eC4wrIUgI6FQ==') AND
					  COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
					  '2023-02-22 10:31:33.328879' ::TIMESTAMPTZ))))
SELECT *
FROM TXN
WHERE rn = 1
ORDER BY COALESCE(debited_at, credited_at, updated_at) DESC LIMIT 30
OFFSET 0;


-- Query with sql join
EXPLAIN
ANALYSE (distsql) WITH ORDER_WITH_TXN AS (SELECT row_number() OVER ( PARTITION BY otm.order_id ORDER BY t.created_at DESC) AS rn,
							   o.id,
							   t.updated_at                                                              AS txn_updated_at,
							   t.credited_at,
							   t.created_at                                                              AS txn_created_at,
							   t.id                                                                      AS txn_id,
							   t.debited_at
						FROM orders_transactions_map otm
								 INNER JOIN transactions t ON otm.transaction_id = t.id
								 INNER JOIN orders o ON o.id = otm.order_id
						WHERE ((t.pi_from IN ('PI220722IRAVUI0IR9maNMKimEYhYw==', 'PI220722VJRMtjGGQmi44cekD2PBYw==',
									'PI220722zHL+799rR+eC4wrIUgI6FQ==') AND COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
																  '2023-02-22 10:31:33.328879' ::TIMESTAMPTZ) OR
							   (t.pi_to IN ('PI220722IRAVUI0IR9maNMKimEYhYw==', 'PI220722VJRMtjGGQmi44cekD2PBYw==',
									'PI220722zHL+799rR+eC4wrIUgI6FQ==') AND COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
																'2023-02-22 10:31:33.328879' ::TIMESTAMPTZ))
						  AND ((o.from_actor_id = 'AC220722Rrlb+LrhTXSpOOPlX9b23Q==') OR (o.to_actor_id = 'AC220722Rrlb+LrhTXSpOOPlX9b23Q=='))
						  AND ((o.status = 'PAID' AND o.workflow NOT IN
													  ('ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD',
													   'ADD_FUNDS_COLLECT', 'COLLECT_RECURRING_PAYMENT_NO_AUTH',
													   'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
							   (o.status = 'SETTLED' AND
								o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
							   (o.status = 'FULFILLED' AND
								o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
							   (o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT'))))
SELECT *
FROM ORDER_WITH_TXN
WHERE rn = 1
ORDER BY COALESCE(debited_at, credited_at, txn_updated_at) DESC LIMIT 30
OFFSET 0;


-- Query with sql join with lower bound
EXPLAIN
ANALYSE (distsql) WITH ORDER_WITH_TXN AS (SELECT row_number() OVER ( PARTITION BY otm.order_id ORDER BY t.created_at DESC) AS rn,
							   o.id,
							   t.updated_at                                                              AS txn_updated_at,
							   t.credited_at,
							   t.created_at                                                              AS txn_created_at,
							   t.id                                                                      AS txn_id,
							   t.debited_at
						FROM orders_transactions_map otm
								 INNER JOIN transactions t ON otm.transaction_id = t.id
								 INNER JOIN orders o ON o.id = otm.order_id
						WHERE ((t.pi_from IN ('PI220722IRAVUI0IR9maNMKimEYhYw==', 'PI220722VJRMtjGGQmi44cekD2PBYw==',
									'PI220722zHL+799rR+eC4wrIUgI6FQ==') AND COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
																  '2023-03-03 13:01:10.476618' ::TIMESTAMPTZ AND COALESCE(t.debited_at, t.credited_at, t.updated_at) >= '2023-03-03 11:01:10.476774' ::TIMESTAMPTZ) OR
							   (t.pi_to IN ('PI220722IRAVUI0IR9maNMKimEYhYw==', 'PI220722VJRMtjGGQmi44cekD2PBYw==',
									'PI220722zHL+799rR+eC4wrIUgI6FQ==') AND COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
																'2023-03-03 13:01:10.476618' ::TIMESTAMPTZ AND COALESCE(t.debited_at, t.credited_at, t.updated_at) >= '2023-03-03 11:01:10.476774' ::TIMESTAMPTZ))
						  AND ((o.from_actor_id = 'AC220722Rrlb+LrhTXSpOOPlX9b23Q==') OR (o.to_actor_id = 'AC220722Rrlb+LrhTXSpOOPlX9b23Q=='))
						  AND ((o.status = 'PAID' AND o.workflow NOT IN
													  ('ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD',
													   'ADD_FUNDS_COLLECT', 'COLLECT_RECURRING_PAYMENT_NO_AUTH',
													   'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
							   (o.status = 'SETTLED' AND
								o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
							   (o.status = 'FULFILLED' AND
								o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
							   (o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT'))))
SELECT *
FROM ORDER_WITH_TXN
WHERE rn = 1
ORDER BY COALESCE(debited_at, credited_at, txn_updated_at) DESC LIMIT 30
OFFSET 0;
