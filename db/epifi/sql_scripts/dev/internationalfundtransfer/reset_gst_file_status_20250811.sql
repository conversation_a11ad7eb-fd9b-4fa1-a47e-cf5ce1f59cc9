-- Making the file as invalid so that it doesn't get picked up for processing
-- New files will be generated for the same batch id

update foreign_remittance_file_generation_attempts
set client_request_id='USSRP2aVuUhmugX250804_GST_TTUM_old', file_status='FILE_STATUS_INVALID', updated_at = now()
where id = '12db2187-bef9-4b38-953b-78a6c46f703f';

update foreign_remittance_file_generation_attempts
set client_request_id='USSRP2CFFMQjZ7T250806_GST_TTUM_old', file_status='FILE_STATUS_INVALID', updated_at = now()
where id = '8edec91f-6613-405e-b5a8-b5239b85e160';

update foreign_remittance_file_generation_attempts
set client_request_id='USSRP3EwDf8TXRX250805_GST_TTUM_old', file_status='FILE_STATUS_INVALID', updated_at = now()
where id = '03a0d907-9b79-4de2-9edb-99de4bb2588a';
