-- select all the pending transactions without serialID for actor `AC201229ubgnAjpQRxyKbMRCyeE/RA==`
SELECT id
FROM transactions
WHERE (pi_from IN
       ('PI210104TB67PKVqR1GoIgoFEp/qxA==', 'PI210104mJAZmwxiQFqYwPIEeieQYA==', 'PI210105YksIxr8OQHil7CZZ8iINUA==',
        'PI210114WwObyhV/Rzu4J2IWlUWtQQ==', 'PI2101153sd1DWvHTai7NAKS22SzUw==', 'PI2101156ROqs9XNSWKApuw2zJYSWQ==',
        'PI210127LKF1MlwVRqOgc8FznSQ4KA==', 'PI210310MEHMLbiLQMaZo3aA5kmNEQ==', 'PI210310q3lwrI/JQ1KMWeYe2IPBMQ==',
        'PI210416IOcZjzZeSg6pO30FDDa8/A==', 'PI210416hI1P9kj0TAChGw2oyDfLQA==', 'PI210417Xxyzem56RTaHEFFwe2M9Gw==') OR
       pi_to IN
       ('PI210104TB67PKVqR1GoIgoFEp/qxA==', 'PI210104mJAZmwxiQFqYwPIEeieQYA==', 'PI210105YksIxr8OQHil7CZZ8iINUA==',
        'PI210114WwObyhV/Rzu4J2IWlUWtQQ==', 'PI2101153sd1DWvHTai7NAKS22SzUw==', 'PI2101156ROqs9XNSWKApuw2zJYSWQ==',
        'PI210127LKF1MlwVRqOgc8FznSQ4KA==', 'PI210310MEHMLbiLQMaZo3aA5kmNEQ==', 'PI210310q3lwrI/JQ1KMWeYe2IPBMQ==',
        'PI210416IOcZjzZeSg6pO30FDDa8/A==', 'PI210416hI1P9kj0TAChGw2oyDfLQA==', 'PI210417Xxyzem56RTaHEFFwe2M9Gw=='))
  AND ( array_length(regexp_split_to_array(utr, ':'), 1) = 2 -- filter cases <date>:cbsID
    OR (array_length(regexp_split_to_array(utr, ':'), 1) = 3 AND
        regexp_split_to_array(utr, ':')[1] = 'EXTENDED') ) -- filter cases EXTENDED:<date>:cbsID
  AND partner_bank = 'FEDERAL_BANK';
