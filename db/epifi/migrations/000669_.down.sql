CREATE TABLE IF NOT EXISTS comms_messages (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	client_id STRING NULL,
	sqs_msg_id STRING NULL,
	vendor STRING NULL,
	vendor_msg_id STRING NULL,
	retries INT8 NULL,
	status STRING NULL,
	medium STRING NULL,
	created_at TIMESTAMP NULL DEFAULT now():::TIMESTAMP,
	updated_at TIMESTAMP NULL DEFAULT now():::TIMESTAMP,
	actor_id STRING NULL,
	user_identifier_type STRING NULL,
	user_identifier_value STRING NULL,
	qos STRING NULL,
	metadata JSONB NULL,
	external_reference_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX actor_id_idx (actor_id ASC),
	INDEX vendor_msg_id_idx (vendor_msg_id ASC),
	INDEX user_identifier_value_index (user_identifier_value ASC),
	INDEX comms_messages_updated_at_idx (updated_at ASC),
	INDEX comms_messages_external_reference_id_client_id_idx (external_reference_id ASC, client_id ASC)
);

CREATE TABLE IF NOT EXISTS fcm_device_tokens (
  id UUID NOT NULL DEFAULT gen_random_uuid(),
  actor_id STRING NULL,
  device_token STRING NULL,
  status STRING NULL,
  created_at TIMESTAMP NULL DEFAULT now():::TIMESTAMP,
  updated_at TIMESTAMP NULL DEFAULT now():::TIMESTAMP,
  CONSTRAINT "primary" PRIMARY KEY (id ASC),
  INDEX fcm_device_tokens_updated_at_idx (updated_at ASC),
  INDEX fcm_device_tokens_actor_id_idx (actor_id ASC),
  INDEX fcm_device_tokens_device_token_idx (device_token ASC)
);

CREATE TABLE IF NOT EXISTS email_callbacks (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	message_id STRING NOT NULL,
	event_type STRING NOT NULL,
	event_timestamp TIMESTAMPTZ NOT NULL,
	vendor STRING NOT NULL,
	event_meta JSONB NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT email_callbacks_pkey PRIMARY KEY (id ASC),
	INDEX email_callbacks_message_id_vendor_idx (message_id ASC, vendor ASC),
	INDEX email_callbacks_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE email_callbacks IS 'table to store email events received in callbacks for various events on email';
COMMENT ON COLUMN email_callbacks.event_type IS '{"proto_type":"comms.EventType", "comment": "enum to store type of event for which callback is received"}';
COMMENT ON COLUMN email_callbacks.vendor IS '{"proto_type":"vendorgateway.Vendor", "comment": "enum to store vendor information"}';
COMMENT ON COLUMN email_callbacks.event_meta IS '{"proto_type":"comms.EventMeta", "comment": "JSONB to store meta data for an event"}';

CREATE TABLE IF NOT EXISTS email_templates (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	email_type STRING NOT NULL,
	template_version STRING NOT NULL,
	subject STRING NOT NULL,
	template STRING NOT NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT email_templates_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX email_templates_email_type_version_key (email_type ASC, template_version ASC),
	INDEX email_templates_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE email_templates IS 'table to store email templates to be used by comms service while sending emails';
COMMENT ON COLUMN email_templates.email_type IS '{"proto_type":"comms.EmailType", "comment": "enum to store list of all possible email types that can be sent via comms service"}';
COMMENT ON COLUMN email_templates.template_version IS '{"proto_type":"comms.TemplateVersion", "comment": "enum to store list of possible versions for a template"}';

CREATE TABLE IF NOT EXISTS comms_notifications (
	message_id UUID NOT NULL,
	actor_id STRING NOT NULL,
	type STRING NOT NULL,
	expire_at TIMESTAMPTZ NOT NULL,
	data JSONB NOT NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	priority VARCHAR NULL,
	in_app_section VARCHAR NULL,
	sub_status VARCHAR NULL,
	CONSTRAINT "primary" PRIMARY KEY (message_id ASC),
	INDEX comms_notifications_updated_at_idx (updated_at ASC),
	INDEX comms_notifications_actor_id_expire_at_idx (actor_id ASC, expire_at ASC) STORING (type, status, created_at),
	FAMILY "primary" (message_id, actor_id, type, expire_at, data, created_at, priority, in_app_section, sub_status),
	FAMILY frequently_updated (status, updated_at)
);
COMMENT ON TABLE comms_notifications IS 'table to store details of app notifications sent to users';
COMMENT ON COLUMN comms_notifications.type IS '{"proto_type":"comms.NotificationType", "comment": "type of notification"}';
COMMENT ON COLUMN comms_notifications.data IS '{"proto_type":"comms.NotificationData", "comment": "Notification data used for sending message"}';
COMMENT ON COLUMN comms_notifications.status IS '{"proto_type":"comms.NotificationStatus", "comment": "status of notification"}';
COMMENT ON COLUMN comms_notifications.priority IS '{"proto_type":"comms.InAppNotificationPriority"}';
COMMENT ON COLUMN comms_notifications.in_app_section IS '{"proto_type":"comms.InAppNotificationSection"}';
COMMENT ON COLUMN comms_notifications.sub_status IS '{"proto_type":"comms.NotificationSubStatus"}';

CREATE TABLE IF NOT EXISTS sms_callbacks (
	response_id STRING NOT NULL,
	status STRING NOT NULL,
	vendor STRING NOT NULL,
	callback_info JSONB NULL,
	vendor_updated_time TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT sms_callbacks_pkey PRIMARY KEY (response_id ASC, vendor ASC),
	INDEX sms_callbacks_updated_at_idx (updated_at ASC)
);

CREATE TABLE IF NOT EXISTS sms_templates (
	id INT8 NOT NULL DEFAULT unique_rowid(),
	template_type STRING NOT NULL,
	template STRING NOT NULL,
	dlt_template_id STRING NOT NULL,
	version STRING NOT NULL,
	is_deleted BOOL NOT NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT sms_templates_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX sms_template_type_version_key (template_type ASC, version ASC),
	INDEX sms_templates_updated_at_idx (updated_at ASC)
);

CREATE TABLE IF NOT EXISTS user_comms_preferences (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	medium STRING NOT NULL,
	category STRING NOT NULL,
	preference STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NULL,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX actor_id_medium_category_preference_deleted_at_unique_idx (actor_id ASC, medium ASC, category ASC, deleted_at_unix ASC),
	INDEX user_comms_preferences_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE user_comms_preferences IS 'table to store user preferences for receiving comms via various modes for various categories';
COMMENT ON COLUMN user_comms_preferences.medium IS '{"proto_type":"comms.Medium", "comment": "medium of communication"}';
COMMENT ON COLUMN user_comms_preferences.category IS '{"proto_type":"comms.Category", "comment": "category of communication"}';
COMMENT ON COLUMN user_comms_preferences.preference IS '{"proto_type":"comms.user_preference.Preference", "comment": "Preference for receiving communication"}';

CREATE TABLE IF NOT EXISTS whatsapp_callbacks (
	response_id STRING NOT NULL,
	status STRING NOT NULL,
	vendor STRING NOT NULL,
	dlr_received_time TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT whatsapp_callbacks_pkey PRIMARY KEY (response_id ASC, vendor ASC),
	INDEX whatsapp_callbacks_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE whatsapp_callbacks IS 'table to store status and other details sent by whatsapp vendors in callback';
COMMENT ON COLUMN whatsapp_callbacks.status IS '{"proto_type":"comms.MessageState", "comment": "state of message"}';
COMMENT ON COLUMN whatsapp_callbacks.vendor IS '{"proto_type":"vendorgateway.Vendor", "comment": "vendor used for sending message"}';

CREATE TABLE IF NOT EXISTS whatsapp_message_histories (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	response_id STRING NOT NULL,
	status STRING NOT NULL,
	status_timestamp TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT whatsapp_message_histories_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX response_id_status (response_id ASC, status ASC),
	INDEX whatsapp_message_histories_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE whatsapp_message_histories IS 'table to store message history related to whatsapp message sent by us to user to capture times of sent, delivered and read';
COMMENT ON COLUMN whatsapp_message_histories.status IS '{"proto_type":"comms.WhatsappMessageStatus", "comment": "enum to store list of possible status of whatsapp message status after it is sent to vendor : sent, read, delivered"}';

CREATE TABLE IF NOT EXISTS whatsapp_user_replies (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	response_id STRING NOT NULL,
	response_info JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT whatsapp_user_replies_pkey PRIMARY KEY (id ASC),
	INDEX whatsapp_user_replies_updated_at_idx (updated_at ASC),
	INDEX whatsapp_user_replies_response_id_index (response_id ASC)
);
COMMENT ON TABLE whatsapp_user_replies IS 'table to store user reply details for whatsapp communication';
COMMENT ON COLUMN whatsapp_user_replies.response_info IS '{"proto_type":"comms.WhatsAppUserReplyInfo", "comment": "user reply detailed info"}';

CREATE TABLE IF NOT EXISTS notifications_user_level_actions (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id VARCHAR NOT NULL,
	action VARCHAR NOT NULL,
	action_timestamp TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, id ASC),
	UNIQUE INDEX actor_id_event_key (actor_id ASC, action ASC),
	INDEX notifications_user_level_actions_updated_at_index (updated_at ASC)
);
COMMENT ON TABLE notifications_user_level_actions IS 'table to keep the latest timestamp of user level notifications events like view_all, dismiss_all etc';
COMMENT ON COLUMN notifications_user_level_actions.action IS '{"proto_type":"comms.NotificationAction"}';
