-- slack ref: https://epifi.slack.com/archives/C031GNXERB9/p1714395532218069
-- updating recurring payment action status to success as the the corresponding payment order is in terminal

update recurring_payments_actions
set state = 'ACTION_SUCCESS', updated_at = NOW()
where id in    ('03e6a85b-9c91-4f2c-9b36-abea81d1fdc0',
				'14d06266-b90f-452d-803c-de2ba2d3b4e7',
				'1f3c58ce-62e1-435e-aaf0-885b5eda6a2e',
				'6a585210-76bf-4bf2-9c3d-c6280a8e0640',
				'0eeea7ce-cd23-4acc-a971-b60a8f01874d',
				'30e21d4d-9eb8-4ff8-b7f7-7620aa4642a3',
				'09d206fc-c44f-41d4-938e-f7199d979af9',
				'd1e2e86e-c953-4260-b5cf-e5074ffefb83',
				'2d9d52f9-09c3-41ca-975d-e64f8e0e3e25',
				'46d4e894-4d12-4766-a289-7395fbbc5df2',
				'8335fc80-2a32-4a7f-9fb8-d12c827d09f0',
				'15b59961-ba88-4a4e-a8f1-5e88edb8ba17',
				'67da065e-8f4d-4b24-884b-7a05f2548a9b',
				'3d2959d3-f3b5-42fb-ad93-1b71fb8b5f78',
				'8c6437e4-0b57-41fc-affb-42abc80869f7',
				'817c422c-ecf5-4e93-b187-72217820ce32',
				'61581b6f-27b4-4a2d-bc9d-69515dcb13e1',
				'bbc1473d-79d7-42f1-92f5-f7a54e498d88')

  and client_request_id in ('bb2b0413-3033-428f-bb75-ac10215cdd3a',
							'd3b6a04a-fed9-4caa-9c93-5d9c2685828c',
							'1605b83c-92ee-44a7-aaa6-8a885f60830e',
							'9a3728a4-9bb3-4f79-812a-8b436436ae3b',
							'bf887528-daaa-488b-b3c9-15a6486c9d1b',
							'dfa9bc27-b0d4-4b8f-ad5d-a3585719f20e',
							'44ca7fb3-fe9d-4849-b54b-c24e8d3ea174',
							'79df45b3-3296-49ea-84f1-7106b5b60da0',
							'6aaaa5d2-399a-424b-bec3-28ac8b566158',
							'eb99fa79-d740-412e-a333-744377bdbd8b',
							'd15af772-9c2d-4963-b4ea-4d4495a3c53f',
							'7b2eb397-714b-4b22-a3d8-be7cabadc041',
							'06e28738-d0cd-44b2-8a06-0b2e875419cc',
							'7cd12f55-24c0-4a29-98ef-65511d3348f1',
							'5628ff79-8833-46bc-ab20-25d917ef0f2c',
							'a183e00b-e34a-4058-94db-a5f8132763e2',
							'3c796098-ffb6-4b57-96a5-f7fe99f84f6d',
							'4c2d6f48-b5f7-4b4b-8959-55eaba02d1d8');
