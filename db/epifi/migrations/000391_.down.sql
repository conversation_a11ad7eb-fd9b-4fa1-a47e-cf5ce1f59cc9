ALTER TABLE workflow_histories DROP COLUMN IF EXISTS wf_req_id;

drop table if exists workflow_requests;

create table if not exists workflow_requests (
	id               UUID      PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
	actor_id         STRING    NULL,
	stage            STRING    NOT NULL,
	status           STRING    NOT NULL,
	version          STRING    NOT NULL,
	type             STRING    NOT NULL,
	payload          BYTES     NULL,
	client_req_id    BYTES    NOT NULL,
	created_at          TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at          TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at          TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ
	);

COMMENT ON COLUMN workflow_requests.payload IS 'An opaque blob containing the data needed for processing activity for a workflow.
     This might vary based on the type of workflow. The data inside the blob will depend on underlying domain service';
COMMENT ON COLUMN workflow_requests.client_req_id IS 'Client details corresponding to the service initiating workflow request.
     The combination of client and client_req_id must be unique';

ALTER TABLE workflow_histories ADD COLUMN IF NOT EXISTS wf_req_id UUID;
ALTER TABLE workflow_histories ADD CONSTRAINT workflow_history_wf_req_id_fkey FOREIGN KEY (wf_req_id) REFERENCES workflow_requests (id);
COMMENT ON COLUMN public.workflow_histories.wf_req_id IS 'foreign key from workflows req table';
