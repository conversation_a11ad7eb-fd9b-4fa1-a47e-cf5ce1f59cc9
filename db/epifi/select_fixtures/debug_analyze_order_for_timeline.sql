-- query without index hint
explain analyze (debug )SELECT  o.id FROM orders o WHERE (o.created_at <= '2022-08-16 22:08:26.71' ::TIMESTAMPTZ AND o.status NOT IN ('CREATED','REJECTED') AND (( o.from_actor_id = 'AC210902UDY+LpGES6mkLL41SrfMuQ==' AND o.to_actor_id = 'AC2103240dzOiSO9Tmm/7rh25LPEJw==' AND IF( o.workflow IN ('P2P_COLLECT_SHORT_CIRCUIT') , o.status NOT IN ('COLLECT_FAILED','COLLECT_IN_PROGRESS','CREATED'), true) )OR ( o.from_actor_id = 'AC2103240dzOiSO9Tmm/7rh25LPEJw==' AND o.to_actor_id = 'AC210902UDY+LpGES6mkLL41SrfMuQ==' AND IF( o.workflow IN ('P2P_COLLECT_SHORT_CIRCUIT','P2P_COLLECT','ADD_FUNDS','ADD_FUNDS_COLLECT','P2P_COLLECT','COLLECT_RECURRING_PAYMENT_WITH_AUTH','COLLECT_RECURRING_PAYMENT_NO_AUTH') , true, o.status in ('PAID','FULFILLED') ) ) ) )ORDER BY  o.created_at DESC LIMIT 30 OFFSET 0;
-- query with index hint
explain analyze (debug )SELECT  o.id FROM orders@orders_outbound_actor_lookup_idx o WHERE (o.created_at <= '2022-08-16 22:08:26.71' ::TIMESTAMPTZ AND o.status NOT IN ('CREATED','REJECTED') AND (( o.from_actor_id = 'AC210902UDY+LpGES6mkLL41SrfMuQ==' AND o.to_actor_id = 'AC2103240dzOiSO9Tmm/7rh25LPEJw==' AND IF( o.workflow IN ('P2P_COLLECT_SHORT_CIRCUIT') , o.status NOT IN ('COLLECT_FAILED','COLLECT_IN_PROGRESS','CREATED'), true) )OR ( o.from_actor_id = 'AC2103240dzOiSO9Tmm/7rh25LPEJw==' AND o.to_actor_id = 'AC210902UDY+LpGES6mkLL41SrfMuQ==' AND IF( o.workflow IN ('P2P_COLLECT_SHORT_CIRCUIT','P2P_COLLECT','ADD_FUNDS','ADD_FUNDS_COLLECT','P2P_COLLECT','COLLECT_RECURRING_PAYMENT_WITH_AUTH','COLLECT_RECURRING_PAYMENT_NO_AUTH') , true, o.status in ('PAID','FULFILLED') ) ) ) )ORDER BY  o.created_at DESC LIMIT 30 OFFSET 0;
