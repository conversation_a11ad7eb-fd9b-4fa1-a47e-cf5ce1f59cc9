-- Making the file as invalid so that it doesn't get picked up for processing
-- New files will be generated for the same batch id

update foreign_remittance_file_generation_attempts
set client_request_id='USSRP2aVuUhmugX250804_GST_old', file_status='FILE_STATUS_INVALID', updated_at = now()
where id = '709a6674-052d-4bc7-9155-ae0198394da3';

update foreign_remittance_file_generation_attempts
set client_request_id='USSRP2CFFMQjZ7T250806_GST_old', file_status='FILE_STATUS_INVALID', updated_at = now()
where id = '304ff073-d3b9-468e-8df9-721c5d4e5828';

update foreign_remittance_file_generation_attempts
set client_request_id='USSRP3EwDf8TXRX250805_GST_old', file_status='FILE_STATUS_INVALID', updated_at = now()
where id = 'd4d41955-72ec-4aa0-94ba-be35782a6b3e';
