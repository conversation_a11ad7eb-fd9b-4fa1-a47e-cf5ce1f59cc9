SELECT id, utr
FROM transactions
WHERE array_length(regexp_split_to_array(utr, ':'), 1) = 2 -- filter cases <date>:cbsID
    AND regexp_split_to_array(utr, ':')[1] != 'EXTENDED' -- filter cases EXTENDED:utr
    AND regexp_split_to_array(utr, ':')[1] != 'CREDIT' -- filter cases CREDIT:utr
    AND regexp_split_to_array(utr, ':')[1] != 'DEBIT' -- filter cases DEBIT:utr
    AND partner_bank = 'FEDERAL_BANK';
