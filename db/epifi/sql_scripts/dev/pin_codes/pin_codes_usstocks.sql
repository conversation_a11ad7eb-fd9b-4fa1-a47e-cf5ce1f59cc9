INSERT INTO pin_code_master (pin_code, division, region, circle, taluk, district, state)
VALUES ('205130', 'Mainpuri', 'Agra', 'Uttar Pradesh', 'Jasarana', 'Firozabad', 'Uttar Pradesh'),
	   ('263002', 'NAINITAL', 'DEH<PERSON>D<PERSON>', 'UTTARAKHAND', 'NAINITAL', 'NAINITAL', 'UTTARAKHAND')
ON CONFLICT (pin_code, division, region, circle, post_office, district, state)
	DO UPDATE
	SET pin_code = EXCLUDED.pin_code,
		division = EXCLUDED.division,
		region   = EXCLUDED.region,
		circle   = EXCLUDED.circle,
		taluk    = EXCLUDED.taluk,
		district = EXCLUDED.district,
		state    = EXCLUDED.state;
