-- This fixture is to update payment protocol for given order ids to process DC_forex_refund for these transactions
-- For these transactions, dc_forex entry is marked as capped incorrectly due to bank side changes in the procedure to charge forex fee.

UPDATE transactions
SET payment_protocol = 'CARD',
	updated_at = now()
where
	order_ref_id in (
					 'ODir4wSttJQBGnN3UarQYDMg240826==',
					 'ODowghzMiFQ/OjBZ7bpqrGEQ240824==',
					 'OD1xSvI6BeTSegbjcPk+RLkQ240827==',
					 'ODPiiQQCQTTsulwtlQ8+XqjQ240824==',
					 'ODInQeJU/zQRu6N3yw97p9KQ240826==',
					 'ODYAI+8emyRFK2H2THYc48dg240824=='
		);



