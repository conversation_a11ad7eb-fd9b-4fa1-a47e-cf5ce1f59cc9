--CX issue Ticket: 4206169, Due to different transparticular, below Deposit is not marked as CLOSED. Deposit closures were credited to user
--Deposit Details
--deposit id: DPfV8Blmi/RLONrjArcxgN/w230328== closed manually on 16-April, 2024
--"ActorId": AC220112O4LOZAPNStqY5fl8YNlMUA==


-- CX Issue Ticket - 4412957
-- Manually updating  the deposit state as we received incorrect transparticulars, due to this
-- deposit state was not updated to closed. deposit id = DP221124z0CuIp++QI+83APsd1rmtA==
-- slack thread - https://epifi.slack.com/archives/C057JMP5US3/p1716017195307549

-- CX Issue Ticket - 4349497
-- Manually updating  the deposit state as we received incorrect transparticulars, due to this
-- deposit state was not updated to closed. deposit id = DPqEkv6NK/TUuld4Y9gHmWOw240410==
-- UTR: 151:********:S8259784

UPDATE deposit_accounts
set state='CLOSED' , updated_at = NOW()
where state = 'CREATED'
  and id = 'DPqEkv6NK/TUuld4Y9gHmWOw240410=='
  and actor_id ='AC211108chgImE7LSF+oEhUYyuFXWg==' ;

