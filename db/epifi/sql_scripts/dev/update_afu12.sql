-- https://monorail.pointz.in/p/fi-app/issues/detail?id=93053

--user 1
update auth_factor_updates
set overall_status = 'OVERALL_STATUS_IN_PROGRESS',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = '19684500-5f87-4171-b80d-71dcb0f3aa74';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{requestId}', to_jsonb('DevReRegSun20250119120314MnRUu'))
where id = '19684500-5f87-4171-b80d-71dcb0f3aa74';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED'))
where id = '19684500-5f87-4171-b80d-71dcb0f3aa74';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_UNSPECIFIED'))
where id = '19684500-5f87-4171-b80d-71dcb0f3aa74';


--user 2
update auth_factor_updates
set overall_status = 'OVERALL_STATUS_IN_PROGRESS',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
	where id = '1d3146a2-0dd7-477d-be28-2709eb0d1e62';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{requestId}', to_jsonb('DevReRegSun2025012612433755QEj'))
where id = '1d3146a2-0dd7-477d-be28-2709eb0d1e62';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED'))
where id = '1d3146a2-0dd7-477d-be28-2709eb0d1e62';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_UNSPECIFIED'))
where id = '1d3146a2-0dd7-477d-be28-2709eb0d1e62';
