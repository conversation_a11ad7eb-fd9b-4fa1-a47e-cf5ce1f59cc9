ALTER TABLE screener_attempts DROP COLUMN IF EXISTS client;
ALTER TABLE employment_verification_processes DROP COLUMN IF EXISTS client CASCADE;
DROP INDEX IF EXISTS employment_verification_processes_result_client_computed_work_email_deleted_at_idx;
CREATE INDEX IF NOT EXISTS employment_verification_processes_verification_result_computed_work_email_deleted_at_idx ON employment_verification_processes (verification_result, computed_work_email, deleted_at);
