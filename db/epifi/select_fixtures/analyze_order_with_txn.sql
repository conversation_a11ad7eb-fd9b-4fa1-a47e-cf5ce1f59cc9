-- explain analyze for order_with_txn_query
explain analyze (distsql )WITH ORDER_WITH_TXN AS ( SELECT distinct o.id, o.created_at ,  t.created_at AS txn_created_at,  t.status AS txn_status, t.id AS txn_id  FROM orders o LEFT JOIN transactions t ON o.id = t.order_ref_id WHERE IF ( t.id IS NOT NULL, ( ( t.pi_from IN ('PI220112MFUVpzfqTzWDt8r/tLYOLw==','PI220112b1YIp58tT8mrGaX/XP7L4A==','PI220112nVj2M+JRSva821++QCp3kw==')) OR ( t.pi_to IN ('PI220112MFUVpzfqTzWDt8r/tLYOLw==','PI220112b1YIp58tT8mrGaX/XP7L4A==','PI220112nVj2M+JRSva821++QCp3kw==')) ) AND t.created_at <= '2022-08-12 12:58:41.372' ::TIMESTAMPTZ , o.workflow = 'P2P_COLLECT_SHORT_CIRCUIT' )AND o.created_at <= '2022-08-12 12:58:41.372' ::TIMESTAMPTZ AND o.status NOT IN ('CREATED','REJECTED') AND (( o.from_actor_id = 'AC220107cgeCfdkTTA+55KHkFqZz/w==' AND o.to_actor_id = 'AC210821aRZmjyOCRkG5Xyva0QS5BA==' AND IF( o.workflow IN ('P2P_COLLECT_SHORT_CIRCUIT') , o.status NOT IN ('COLLECT_FAILED','COLLECT_IN_PROGRESS','CREATED'), true) )OR ( o.to_actor_id = 'AC220107cgeCfdkTTA+55KHkFqZz/w==' AND o.from_actor_id = '' AND IF( o.workflow IN ('P2P_COLLECT_SHORT_CIRCUIT','P2P_COLLECT','ADD_FUNDS','ADD_FUNDS_COLLECT','P2P_COLLECT','COLLECT_RECURRING_PAYMENT_WITH_AUTH','COLLECT_RECURRING_PAYMENT_NO_AUTH') , true, o.status in ('PAID','FULFILLED') ) ) ) ) SELECT * FROM ORDER_WITH_TXN  ORDER BY IFNULL(txn_created_at, created_at) DESC LIMIT 3 OFFSET 0;
