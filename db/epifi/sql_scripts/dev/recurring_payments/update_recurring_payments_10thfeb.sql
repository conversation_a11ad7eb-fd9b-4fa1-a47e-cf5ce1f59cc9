--First Query: Updates the record with ID c60a6258-97f9-4300-923b-9906f5249622 to transition its state to ACTION_SUCCESS and updates the updated_at timestamp.
--Second Query: Soft deletes the record with ID 3701ef60-177a-4e5c-b48a-19021efc110e by setting the deleted_at timestamp to NOW() and updates the updated_at timestamp.

UPDATE recurring_payments_actions
SET  state = 'ACTION_SUCCESS', updated_at = NOW()
WHERE id = 'c60a6258-97f9-4300-923b-9906f5249622'
  AND client_request_id = 'f73241cf-01ae-4c5b-ba86-51fa0f5da617'
  AND state = 'ACTION_CREATED'
  AND action = 'EXECUTE';

UPDATE recurring_payments_actions
SET deleted_at = NOW(), updated_at = NOW()
WHERE id = '3701ef60-177a-4e5c-b48a-19021efc110e'
  AND client_request_id = 'f73241cf-01ae-4c5b-ba86-51fa0f5da617'
  AND state = 'ACTION_SUCCESS'
  AND action = 'EXECUTE';


