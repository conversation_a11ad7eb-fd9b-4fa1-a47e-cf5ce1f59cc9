-- Update panName in DATA_TYPE_PAN_NAME for the specified user
UPDATE public.users
SET data_verification_details = jsonb_set(
    data_verification_details,
    '{dataVerificationDetails}',
    (
        SELECT jsonb_agg(
            CASE
                WHEN elem->>'dataType' = 'DATA_TYPE_PAN_NAME' THEN
                    jsonb_set(
                        jsonb_set(
                            jsonb_set(elem, '{panName,firstName}', '"VISHNUVARDHAN"'),
                            '{panName,middleName}', '""'
                        ),
                        '{panName,lastName}', '"PALTHURU"'
                    )
                ELSE elem
            END
        )
        FROM jsonb_array_elements(data_verification_details->'dataVerificationDetails') AS elem
    )
)
WHERE id = '2d6afba0-c494-40e7-8ed2-51fd134a145b';
