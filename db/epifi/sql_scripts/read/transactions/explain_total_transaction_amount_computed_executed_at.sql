-- This query introduces a filter on created_at column to limit the number of rows fetched as well as replaces the
-- usage of COALESCE with computed_executed_at column.
EXPLAIN ANALYZE SELECT sum(computed_amount) FROM transactions
WHERE (pi_from in ('PINa1urqq7Q7mH/RmPo/y11Q230929==','PI210623Ht+p4N+NRQ+NqK5c5ccTzw==','PI210623nAVe69rlQeiOIUpkvETMDA=='))
AND status IN ('SUCCESS','UNKNOWN','MANUAL_INTERVENTION','IN_PROGRESS','INITIATED')
AND (computed_executed_at >= '2025-01-01 00:00:00' ::TIMESTAMPTZ)
AND created_at >= '2025-01-01 00:00:00'
AND ((computed_executed_at <= '2025-01-03 00:00:00' ::TIMESTAMPTZ)
OR created_at <= '2025-01-03 00:00:00')
AND payment_protocol IN ('INTRA_BANK','NEFT','IMPS','RTGS','UPI','CARD','AEPS','SWIFT','ENACH','CTS');
