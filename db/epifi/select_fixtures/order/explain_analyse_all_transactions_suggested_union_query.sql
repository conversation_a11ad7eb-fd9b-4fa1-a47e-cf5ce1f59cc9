-- firing the query suggested by crdb team (tweaked the way of using UNION operator) to see the performance in the prod env
explain analyse (distsql )WITH txn AS (SELECT row_number() OVER (PARTITION BY t.order_ref_id ORDER BY t.computed_executed_at DESC)
                        AS rn,
                    computed_executed_at,
                    status,
                    payment_protocol,
                    order_ref_id,
                    partner_bank,
                    utr,
    				pi_to
             FROM transactions AS t
             WHERE t.status IN ('SUCCESS':::STRING, 'FAILED':::STRING, 'REVERSED':::STRING)
               AND (
                         t.pi_from
                         IN (
                            'PI210806MUORgEWBSKuxXpwcLCgGsA==':::STRING,
                      		'PI210806XMNrC6nSQFS9MqiuuGz5xw==':::STRING,
                     		 'PI210806rzLYGKRbSB+gxnYmFxR1qQ==':::STRING
                             )
                     AND t.computed_executed_at <= current_date()::TIMESTAMPTZ
                 )
             UNION ALL
             SELECT row_number() OVER (PARTITION BY t.order_ref_id ORDER BY t.computed_executed_at DESC)
                        AS rn,
                    computed_executed_at,
                    status,
                    payment_protocol,
                    order_ref_id,
                    partner_bank,
                    utr,
    				pi_from
             FROM transactions AS t
             WHERE t.status IN ('SUCCESS':::STRING)
               AND (
                         t.pi_to
                         IN (
                            'PI210806MUORgEWBSKuxXpwcLCgGsA==':::STRING,
                     	    'PI210806XMNrC6nSQFS9MqiuuGz5xw==':::STRING,
                         	'PI210806rzLYGKRbSB+gxnYmFxR1qQ==':::STRING
                             )
                     AND t.computed_executed_at <= current_date()::TIMESTAMPTZ
                 ))
SELECT *
FROM txn
WHERE rn = 1
ORDER BY computed_executed_at ASC LIMIT 30
OFFSET 0;
