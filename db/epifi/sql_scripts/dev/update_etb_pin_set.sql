-- Updating upi pin set status for a user. His UPI PIN is in ETB_PIN_SET state but due to some issue in client side
-- we are unable to change the pin set status to PIN_SET. Updating it manually to unblock user.
UPDATE upi_account_infos
SET pin_set_state='PIN_SET',
	updated_at = NOW()
WHERE account_id='SV230112uxVsfWrAQ8es5NKj+UP/fw=='
  AND pin_set_state='ETB_PIN_SET';

UPDATE upi_accounts
SET pin_set_status= 'UPI_PIN_SET_STATUS_PIN_SET',
	updated_at = NOW()
where id= '17c071f1-63bf-436d-90eb-c84422796afe'
  AND pin_set_status='UPI_PIN_SET_STATUS_ETB_PIN_SET';
