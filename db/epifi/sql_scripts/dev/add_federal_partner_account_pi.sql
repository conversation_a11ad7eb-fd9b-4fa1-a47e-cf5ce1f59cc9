-- Adding pi for business account
UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
('paymentinstrument-epifi-business-account-2', 'BANK_ACCOUNT', '', '{"account": {"account_type": "CURRENT", "actual_account_number": "**************", "ifsc_code": "FDRL000001", "name": "EPIFI TECHNOLOGIES PRIVATE LIMITED", "secure_account_number": "xxxxxxxxxx7843"}}', 'CREATED', '{"INBOUND_TXN": false, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');
