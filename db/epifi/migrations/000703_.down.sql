ALTER TABLE public.connected_accounts ADD CONSTRAINT fk_actor_id_ref_actors FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.account_pis ADD CONSTRAINT fk_actor_id_ref_actors FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.cards ADD CONSTRAINT fk_actor_id_ref_actors FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.face_match_attempts ADD CONSTRAINT fk_actor_id_ref_actors FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.liveness_attempts ADD CONSTRAINT fk_actor_id_ref_actors FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.referrals ADD CONSTRAINT referrals_actor_id_fkey FOREIGN K<PERSON>Y (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.blocked_actors_map ADD CONSTRAINT fk_blocked_actors_map_actor_id FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.vendor_otps ADD CONSTRAINT fk_vendor_otps_actor_id FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.deposit_accounts ADD CONSTRAINT deposit_accounts_actor_id_fkey FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.deposit_requests ADD CONSTRAINT deposit_requests_actor_id_fkey FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.contacts ADD CONSTRAINT fk_contacts_actor_id FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.disputed_transactions ADD CONSTRAINT fk_actor_id_ref_actors FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.card_sku_overrides ADD CONSTRAINT fk_actor_id_ref_actors FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.device_reg_attempts ADD CONSTRAINT foreign_key_actor_id_ref_actors FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.finite_codes ADD CONSTRAINT finite_code_actor_id_fkey FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.inappreferral_unlocks ADD CONSTRAINT inappreferral_unlocks_actor_id_fkey FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.deposit_account_bonus_payouts ADD CONSTRAINT deposit_account_bonus_payouts_actor_id_fkey FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.card_action_attempts ADD CONSTRAINT fk_card_action_attempts_actor_id FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.goals ADD CONSTRAINT fk_actor_id_ref_actors FOREIGN KEY (actor_id) REFERENCES public.actors(id);
ALTER TABLE public.actor_pi_resolutions ADD CONSTRAINT fk_actor_from_ref_actors FOREIGN KEY (actor_from) REFERENCES public.actors(id);
ALTER TABLE public.actor_pi_resolutions ADD CONSTRAINT fk_actor_to_ref_actors FOREIGN KEY (actor_to) REFERENCES public.actors(id);
ALTER TABLE public.timelines ADD CONSTRAINT fk_primary_actor_id_ref_actors FOREIGN KEY (primary_actor_id) REFERENCES public.actors(id);
ALTER TABLE public.timelines ADD CONSTRAINT fk_secondary_actor_id_ref_actors FOREIGN KEY (secondary_actor_id) REFERENCES public.actors(id);
ALTER TABLE public.orders ADD CONSTRAINT fk_actor_from_ref_actors FOREIGN KEY (from_actor_id) REFERENCES public.actors(id);
ALTER TABLE public.orders ADD CONSTRAINT fk_actor_to_ref_actors FOREIGN KEY (to_actor_id) REFERENCES public.actors(id);
ALTER TABLE public.referral_relations ADD CONSTRAINT referral_relations_referrer_actor_id_fkey FOREIGN KEY (referrer_actor_id) REFERENCES public.actors(id);
ALTER TABLE public.referral_relations ADD CONSTRAINT referral_relations_referee_actor_id_fkey FOREIGN KEY (referee_actor_id) REFERENCES public.actors(id);
ALTER TABLE public.blocked_actors_map ADD CONSTRAINT fk_blocked_actors_map_blocked_actor_id FOREIGN KEY (blocked_actor_id) REFERENCES public.actors(id);
ALTER TABLE public.finite_code_claims ADD CONSTRAINT finite_code_claims_referrer_actor_id_fkey FOREIGN KEY (referrer_actor_id) REFERENCES public.actors(id);
ALTER TABLE public.finite_code_claims ADD CONSTRAINT finite_code_claims_referee_actor_id_fkey FOREIGN KEY (referee_actor_id) REFERENCES public.actors(id);