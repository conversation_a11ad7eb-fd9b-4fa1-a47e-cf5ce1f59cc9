-- fetching finite codes data by ids
select id, claim_limit, claimed_count, is_active, created_at, updated_at, deleted_at
from finite_codes
where id in (
			 'FC2112036hLTzsXYTw60+GWblyC0IQ==',
			 'FC211003wpIfyvPuRJKWHQdchl8FWg==',
			 'FC211127X02ET8KoQJa/iWNeeW+m8g==',
			 'FC210810vpfEW4LjRYqh6IuPZ/WTiA==',
			 'FC210808sTyDI7iRQs2g3mculVdCUA==',
			 'FC220210sm/Tesx7S8WN0mVvyqlSRQ==',
			 'FC2112159qUpBMTaTs6W0+NDUX+ybA==',
			 'FC220806EhNS9antQGqT0XkchTH0Ng==',
			 'FC220721l8/cM4irRkCmgu5CPJvgtg==',
			 'FC220712PUWbLKtiTbCzNOPc5PdIJA==',
			 'FC220227Q21oHNmLSWmgHUMUZjdtWg==',
			 'FC210926h+mD7XMiTFCFPYuEXVIkcA==',
			 'FC2203280E4C6nnZQ2SL2KaI3QBBqg==',
			 'FC220121rC45zqA3Sw+OQGqcY0sSig==',
			 'FC220405rq3VzaNrSIy05CK/9/U7Ww==',
			 'FC2108271cEC7E+7SIi4XoQyRPKhkQ==',
			 'FC2207153OsOG7quRZqUOrFdOnlu2A==',
			 'FC220708V9c+ULwuRki2MLIgXHxoIQ==',
			 'FC211202oChGBl0JQAys4a2A1Rl40g==',
			 'FC211202AZhlKVBQQ2GESXCJygASWw==',
			 'FC211119R1Rfqz0YTbeZwsIAkhWABw==',
			 'FC220717ZhhMiAXMQ+uWL9CpWvof2A==',
			 'FC210808dJGW0yvGTV+yRkfXbDHMzw==',
			 'FC22051823+xXKZiQQ6MpMQ8TNaaQg==',
			 'FC220702dYcICWvtTaWx790zvwnz6A=='
	)
order by updated_at desc;
