-- this is written for validating the hypothesis that the below query will fetch all the records since stats are not refresed at the time added in the filter
EXPLAIN ANALYSE(distsql)
select o.id, o.to_actor_id, o.from_actor_id, o.amount, o.workflow, o.tags, o.status, o.updated_at from
	orders@orders_from_actor_id_updated_at_stores_status_workflow_idx as o where
	( o.from_actor_id = 'AC210806ur05GLNgQM2n7ZnCDk/f5g==' ) AND
	(
			( o.status = 'PAID' AND o.workflow NOT IN ('ADD_FUNDS','REWARDS_CREATE_SD','REWARDS_ADD_FUNDS_SD','ADD_FUNDS_COLLECT','COLLECT_RECURRING_PAYMENT_NO_AUTH','COLLECT_RECURRING_PAYMENT_WITH_AUTH') ) OR
			( o.status = 'SETTLED' AND o.workflow IN ('ADD_FUNDS','ADD_FUNDS_COLLECT','P2P_INVESTMENT') ) OR
			( o.status = 'FULFILLED' AND o.workflow IN ('REWARDS_CREATE_SD','REWARDS_ADD_FUNDS_SD') ) OR
			( o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT') ) OR
			( o.status = 'IN_PAYMENT' AND o.workflow IN ('P2P_FUND_TRANSFER') ) OR
			( o.status = 'PAYMENT_FAILED' AND o.workflow IN ('P2P_FUND_TRANSFER') ) OR
			( o.status = 'PAYMENT_REVERSED' AND o.workflow IN ('P2P_FUND_TRANSFER') ) )  AND
		o.updated_at<= '2023-06-19 13:00:00.000'
order by updated_at DESC offset 0 limit 10;
