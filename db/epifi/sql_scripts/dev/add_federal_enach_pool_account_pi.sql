-- Adding PI for enach pool account
UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification) VALUES
('paymentinstrument-enach-pool-account-federal', 'BANK_ACCOUNT', 'Fi-Federal Bank', '{"account": {"account_type": "CURRENT", "actual_account_number": "**************", "ifsc_code": "FDRL0000379", "name": "EPIFI ENACH POOL ACCOUNT", "secure_account_number": "xxxxxxxxxx0010"}}', 'CREATED', '{"INBOUND_TXN": false, "OUTBOUND_TXN": false}', 0, 'EXTERNAL');