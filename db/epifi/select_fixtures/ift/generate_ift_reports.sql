SELECT id, actor_id, updated_at FROM workflow_requests WHER<PERSON> type = 'INTERNATIONAL_FUND_TRANSFER' AND stage = 'INTERNATIONAL_FUND_TRANSFER_LRS_CHECK_ENQUIRY' AND status = 'BLOCKED';

WITH wf_req AS (SELECT id, updated_at FROM workflow_requests WHERE type = 'INTERNATIONAL_FUND_TRANSFER' AND stage = 'INTERNATIONAL_FUND_TRANSFER_ORDER_FULFILMENT' AND status = 'BLOCKED')
SELECT wf_req.id, wf_req.updated_at, wf_his.payload FROM workflow_histories AS wf_his, wf_req WHERE wf_his.wf_req_id= wf_req.id AND wf_his.stage = 'PAYMENT';

SELECT * FROM (WITH wf_req AS (SELECT id, stage, status, updated_at, payload FROM workflow_requests WHERE type = 'INTERNATIONAL_FUND_TRANSFER' AND stage IN ('INTERNATIONAL_FUND_TRANSFER_LRS_CHECK_ENQUIRY', 'INTERNATIONAL_FUND_TRANSFER_SWIFT_PAYMENT') AND status IN ('BLOCKED', 'INITIATED'))
			   SELECT wf_req.id, wf_req.stage, wf_req.status, wf_req.updated_at, wf_req.payload, wf_his.payload FROM workflow_histories AS wf_his, wf_req WHERE wf_his.wf_req_id = wf_req.id AND wf_his.stage = 'PAYMENT') UNION
SELECT * FROM (WITH wf_req AS (SELECT id, stage, status, updated_at,payload FROM workflow_requests WHERE type = 'INTERNATIONAL_FUND_TRANSFER' AND stage = 'INTERNATIONAL_FUND_TRANSFER_LRS_CHECK_ENQUIRY' AND status = 'PENDING')
			   SELECT wf_req.id, wf_req.stage, wf_req.status, wf_req.updated_at, wf_req.payload, wf_his.payload FROM workflow_histories AS wf_his, wf_req WHERE wf_his.wf_req_id = wf_req.id AND wf_his.stage = 'PAYMENT');
