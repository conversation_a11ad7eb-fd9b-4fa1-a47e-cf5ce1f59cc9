-- lrs_consumed_limit will be overwritten with new financial year lrs consumed limit, so we need to persist previous financial year lrs consumed limit in new column
-- this below script will copy the lrs_consumed_limit to new column last_fy_lrs_consumed_limit of international_fund_transfer_checks table
-- this script will be executed only once on 31st March 2023 around 11:50 PM

UPDATE international_fund_transfer_checks SET last_fy_lrs_consumed_limit = lrs_consumed_limit, updated_at = NOW();
