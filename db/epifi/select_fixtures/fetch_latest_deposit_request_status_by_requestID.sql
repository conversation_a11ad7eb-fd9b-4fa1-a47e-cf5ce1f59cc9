-- Trying to fetch latest deposit create/preclose request status for the below mentioned request id's
--as we are seeing these requests are stuck in "REQUEST_IN_PROGRESS" due to suspect response in DepositEnq
--Helps in checking with federal
select ACTOR_ID,id,REQUEST_ID,CLIENT_REQUEST_ID,DEPOSIT_ACCOUNT_ID,TYP<PERSON>,STATE,CREATED_AT
from deposit_requests where REQUEST_ID in   ('NEOLIFDOSat20230708021802HxH6G',
											 'NEOLIFRDSat202307080205576z3aa',
											 'NEOLIFDOSat202307080243277LCJN',
											 'NEOLIFRDSat20230708025018ojUKM',
											 'NEOLIFRDSat20230708014850CrgNS',
											 'NEOLIFRDSat20230708025639GGbFP',
											 'NEOLIFRDSat20230708021548UZqfQ',
											 'NEOLIFRDSat202307080210418QQ67',
											 'NEOLIFRDSat20230708012019QqdV5',
											 'NEOLIFRDSat20230708013204Aj2DW',
											 'NEOLIFRDSat20230708023503VSuEb',
											 'NEOLIFRDFri20230707110329VRxCE',
											 'NEOLIFRDSat20230708014855C8Xay',
											 'NEOLIFDOSat20230708024656H3dF6',
											 'NEOLIFRDSat202307080112474KEpK',
											 'NEOLIFRDSat202307081224559UoCN',
											 'NEOLIFRDSat20230708120507LDZ2r',
											 'NEOLIFRDSat20230708021548CKoXV',
											 'NEOLIFRDSat20230708013351EFLxs',
											 'NEOLIFRDSat20230708012912NEaYh',
											 'NEOLIFRDSat202307081243477Cy3F',
											 'NEOLIFRDSat20230708012706HeWLA',
											 'NEOLIFRDSat20230708021704QUHxB',
											 'NEOLIFDOSat202307080149564YYth',
											 'NEOLIFRDSat202307080211549fbGc',
											 'NEOLIFDOSat202307081218095e3NM',
											 'NEOLIFDOSat20230708015205vCVPo',
											 'NEOLIFDOSat202307081236333cW2n',
											 'NEOLIFRDSat202307081202587w7Bh',
											 'NEOLIFDOFri2023070711481696SMW',
											 'NEOLIFRDSat202307080103553sK1N',
											 'NEOLIFRDSat20230708014901R28pt',
											 'NEOLIFRDSat20230708012233SJMBX',
											 'NEOLIFRDSat20230708012708DskAF',
											 'NEOLIFRDFri202307070504358cgtk',
											 'NEOLIFRDFri202307071141324fkao',
											 'NEOLIFRDSat20230708023708HgR9P',
											 'NEOLIFRDSat202307080100359o18z',
											 'NEOLIFRDFri202307071111083Lyyj',
											 'NEOLIFRDFri20230707111515VrCWn',
											 'NEOLIFRDSat202307081222113Xrwtr',
											 'NEOLIFRDSat20230708030539Pp8M5',
											 'NEOLIFDOSat20230708030500AvviV',
											 'NEOLIFRDSat20230708030223A4nGL',
											 'NEOLIFRDSat20230708030032RxNpz',
											 'NEOLIFDOSat20230708025902QR2GX',
											 'NEOLIFRDSat20230708025923DSf8f',
											 'NEOLIFRDSat202307080259253DoXK',
											 'NEOLICDAThu20230713041105UKdHK',
											 'NEOLICDASat202307080305142zKW2',
											 'NEOLICDASat202307080258325868H',
											 'NEOLICDASat2023070802591984SHP',
											 'NEOLICDASat20230708025406NMS6U',
											 'NEOLICDASat20230708030108ALSyR',
											 'NEOLICDASat202307080301304UDuv',
											 'NEOLICDASat20230708030155RCa6p',
											 'NEOLICDASat20230708030455Gxzr2',
											 'NEOLICDASat202307080254319mHs1',
											 'NEOLICDASat20230708030657S7uwy',
											 'NEOLICDASat20230708025845AV7b5r',
											 'NEOLICDAThu20230713041655Lbda9',
											 'NEOLICDAThu20230713042342U5xYz',
											 'NEOLICDAThu20230713033146E2Xgi',
											 'NEOLICDAThu20230713032708BaGAyr',
											 'NEOLICDAThu20230713031453G8LaW',
											 'NEOLICDAThu20230713061951QVGHY',
											 'NEOLICDAThu20230713060913UbZMz',
											 'NEOLICDAThu2023071305025723MRd',
											 'NEOLICDAThu202307130301378u8LM',
											 'NEOLICDAThu20230713035401PvTsU',
											 'NEOLICDAThu20230713030725BShV6',
											 'NEOLICDAThu20230713034533QvRSy',
											 'NEOLICDAThu2023071305094159fkB',
											 'NEOLICDAThu202307130308064VB7R',
											 'NEOLICDAThu202307130309512PMvo',
											 'NEOLICDAThu202307130510328sBMG',
											 'NEOLICDAThu202307130500382cLw6',
											 'NEOLICDAThu20230713051211QFJP6',
											 'NEOLICDAThu20230713061807SiN7U',
											 'NEOLICDAThu202307130614418qyh7',
											 'NEOLICDAThu20230713061300RwBTQ',
											 'NEOLICDAThu20230713062551M2HuP',
											 'NEOLICDAThu20230713063855DQUsr',
											 'NEOLICDAThu20230713063038SuTnB',
											 'NEOLICDAThu20230713062002KPhJX',
											 'NEOLICDAThu202307130633292xJ4S',
											 'NEOLICDAThu202307130352198e7jY',
											 'NEOLICDAThu20230713043047Rewcn',
											 'NEOLICDAThu20230713043257AHfVK',
											 'NEOLICDAThu20230713043044NE7TG',
											 'NEOLICDAThu20230713043117UV1Nv',
											 'NEOLICDAThu20230713032540EckgA',
											 'NEOLICDAThu20230713031300913oj',
											 'NEOLICDAThu20230713030720yriAR',
											 'NEOLICDAThu202307130647157HHvT',
											 'NEOLICDAThu202307130622366sg6J',
											 'NEOLICDAThu20230713062211HhMCg',
											 'NEOLICDAThu20230713054515ArW4b',
											 'NEOLICDAThu20230713045757AoepQ',
											 'NEOLICDAThu202307130456084w2Ly',
											 'NEOLICDAThu202307130409496uc7V',
											 'NEOLICDAThu20230713033005Qvs5S',
											 'NEOLICDAThu20230713032809RrL6G',
											 'NEOLICDAThu20230713032716KZwmP',
											 'NEOLICDAThu20230713031908CrCF3',
											 'NEOLICDAThu20230713041103BX6BP',
											 'NEOLICDAThu20230713040704BdvzR',
											 'NEOLICDAThu2023071303344715FKx',
											 'NEOLICDAThu20230713043850Jaf9d',
											 'NEOLICDAThu202307130513035LH2n',
											 'NEOLICDAThu20230713053319R4MFP',
											 'NEOLICDAThu20230713054913Msyr7',
											 'NEOLICDAThu20230713031934RW72v',
											 'NEOLICDAThu20230713041056AnPK3',
											 'NEOLICDAThu20230713032605ANmFB',
											 'NEOLICDAThu20230713030821Rdubn',
											 'NEOLICDAThu20230713030349V7pcW',
											 'NEOLICDAThu202307130313016SiTs',
											 'NEOLICDAThu20230713050119QBMQK',
											 'NEOLICDAThu2023071303252251WV3',
											 'NEOLICDAThu20230713054901JsTHz',
											 'NEOLICDAThu20230713054535SkZyH',
											 'NEOLICDAThu20230713052652BLybP',
											 'NEOLICDAThu20230713061931RrYaD',
											 'NEOLICDAThu202307130648534GvNB',
											 'NEOLICDAThu202307130653013vNCD',
											 'NEOLICDAThu202307130557492TJWE',
											 'NEOLICDAThu20230713064134RUqLK',
											 'NEOLICDAThu202307130516037rrDG',
											 'NEOLICDAThu202307130511355kThM',
											 'NEOLICDAThu20230713031718NYpDL',
											 'NEOLICDAThu20230713030132ToNzi',
											 'NEOLICDAThu20230713062855CDfjx',
											 'NEOLICDAThu202307130435493YyV6',
											 'NEOLICDAThu20230713034648ShtC5',
											 'NEOLICDAThu20230713061138HtEsn',
											 'NEOLICDAThu20230713060747KjYEj',
											 'NEOLICDAThu20230713060854HDUaL',
											 'NEOLICDAThu20230713050201NxHLx',
											 'NEOLICDAThu20230713042236T6RaC',
											 'NEOLICDAThu20230713052214Uetmb',
											 'NEOLICDAThu202307130533304s6Jn',
											 'NEOLICDAThu20230713054525SM942',
											 'NEOLICDAThu20230713054842Bj8qo',
											 'NEOLICDAThu20230713030323QVnqx',
											 'NEOLICDAThu20230713041649gsVQu',
											 'NEOLICDAThu20230713041125PBj7h',
											 'NEOLICDAThu20230713033647JwjdN',
											 'NEOLICDAThu20230713034459LDbpj',
											 'NEOLICDAThu202307130422453cgpo',
											 'NEOLICDAThu20230713065535MKKK7',
											 'NEOLICDAThu202307130557577pPt9',
											 'NEOLICDAThu20230713064313KikmM',
											 'NEOLICDAThu20230713033948VGJYW',
											 'NEOLICDAThu20230713033401AHh7J',
											 'NEOLICDAThu20230713030936CaQcF',
											 'NEOLICDAThu20230713031756EQ3ZH',
											 'NEOLICDAThu20230713052003H7exP',
											 'NEOLICDAThu20230713043433TYNig',
											 'NEOLICDAThu20230713051909PkMVT',
											 'NEOLICDAThu20230713061141Lbrhb',
											 'NEOLICDAThu20230713062423AVdBn',
											 'NEOLICDAThu20230713053142VgiGf',
											 'NEOLICDAThu20230713114124LWLGG',
											 'NEOLICDAThu202307130314598By1m',
											 'NEOLICDAThu20230713030717VddTR',
											 'NEOLICDAThu20230713030855TVgfW',
											 'NEOLICDAThu20230713025957325hN',
											 'NEOLICDAThu2023071303370299Gq2',
											 'NEOLICDAThu20230713035648LokSu',
											 'NEOLICDAThu202307130425079LiC1',
											 'NEOLICDAThu20230713045051Hb1tx',
											 'NEOLICDAThu20230713045237LYDqk',
											 'NEOLICDAThu20230713051054VrbDL',
											 'NEOLICDAThu20230713050309SnLua',
											 'NEOLICDAThu20230713043749UQLEP',
											 'NEOLICDAThu20230713052024FeNar',
											 'NEOLICDAThu20230713045128Ztfo3',
											 'NEOLICDAThu20230713055340PE4XKr',
											 'NEOLICDAThu20230713063024MrqsV',
											 'NEOLICDAThu202307130638047dkT1',
											 'NEOLICDAThu20230713033341KaE3Q',
											 'NEOLICDAThu20230713033427QJJmz',
											 'NEOLICDAThu2023071303260563icr',
											 'NEOLICDAThu20230713043843CPjaa',
											 'NEOLICDAThu20230713044408LbTsh',
											 'NEOLICDAThu202307130315465EP2t',
											 'NEOLICDAThu20230713055615Y2Xim',
											 'NEOLICDAThu20230713055736HwMoS',
											 'NEOLICDAThu20230713060545A29qL',
											 'NEOLICDAThu20230713055708BtxpJ',
											 'NEOLICDAThu20230713055240GJyu3',
											 'NEOLICDAThu202307130552275CRMr',
											 'NEOLICDAThu202307130515453pSMX',
											 'NEOLICDAThu202307130327023RJ2a',
											 'NEOLICDAThu202307130312484G75v',
											 'NEOLICDAThu202307130316278a2WT',
											 'NEOLICDAThu20230713033450QjRM3',
											 'NEOLICDAThu20230713042721AQZok',
											 'NEOLICDAThu20230713063009CTSqa',
											 'NEOLICDAThu20230713062307EgmmR',
											 'NEOLICDAThu202307130521458jhVM',
											 'NEOLICDAThu202307130524383Zmy9',
											 'NEOLICDAThu20230713055053G7aCs',
											 'NEOLICDAThu20230713060506SAw2o',
											 'NEOLICDAFri20230714055656R7a1w',
											 'NEOLICDAFri202307140335518m6h2',
											 'NEOLICDAFri20230714033613Gh6Di',
											 'NEOLICDAFri20230714022838L8kH9',
											 'NEOLICDAFri202307140347008w8H6',
											 'NEOLICDAFri202307140338589xaHg',
											 'NEOLICDAFri20230714032003Jm8f2',
											 'NEOLICDAFri202307140245345ZHei',
											 'NEOLICDAFri20230714035440FKczG',
											 'NEOLICDAFri20230714035649Ugaah',
											 'NEOLICDAFri202307140459507dPZp',
											 'NEOLICDAFri202307140455549r9Qa',
											 'NEOLICDAFri202307140518277YFEi',
											 'NEOLICDAFri20230714055003DKcjb',
											 'NEOLICDAFri20230714055205H3Gms',
											 'NEOLICDAFri20230714053814RTMeW',
											 'NEOLICDAFri2023071405491723Tvc',
											 'NEOLICDAFri202307140456043aCoJ',
											 'NEOLICDAFri20230714055535EhXrX',
											 'NEOLICDAFri20230714030611Txc5E',
											 'NEOLICDAFri20230714021920FXxYQ',
											 'NEOLICDAFri20230714011945CzvsV',
											 'NEOLICDAFri20230714023531L7Swj',
											 'NEOLICDAFri20230714023300Jjkrb',
											 'NEOLICDAFri20230714033806TF1Cx',
											 'NEOLICDAFri20230714045602CYM9d',
											 'NEOLICDAFri2023071404455541Bev',
											 'NEOLICDAFri202307140520403zyHb',
											 'NEOLICDAFri20230714052233GD7iF',
											 'NEOLICDAFri20230714054841UrM4t',
											 'NEOLICDAFri20230714055439UFFQq',
											 'NEOLICDAFri20230714053826BRVuh',
											 'NEOLICDAFri2023071401411851sDV',
											 'NEOLICDAFri20230714023024G7Uon',
											 'NEOLICDAFri202307140109425eKC2',
											 'NEOLICDAFri202307140217545iu7p',
											 'NEOLICDAFri20230714022720CmUMj',
											 'NEOLICDAFri20230714030428SJuYW',
											 'NEOLICDAFri20230714030645FWqGU',
											 'NEOLICDAFri202307140404412feTE',
											 'NEOLICDAFri20230714041826EZwxg',
											 'NEOLICDAFri20230714030359ESH2o',
											 'NEOLICDAFri20230714050437334c1',
											 'NEOLICDAFri20230714041554AHXvF',
											 'NEOLICDAFri20230714055338BfL96',
											 'NEOLICDAFri202307140558168EWG5',
											 'NEOLICDAFri202307140118124oTvx',
											 'NEOLICDAFri20230714030002KqmbB',
											 'NEOLICDAFri20230714030155BrZEn',
											 'NEOLICDAFri20230714034541VGqqk',
											 'NEOLICDAFri20230714022731LP94N',
											 'NEOLICDAFri202307140415417agdU',
											 'NEOLICDAFri20230714050815QhE3P',
											 'NEOLICDAFri20230714042638BMiuZ',
											 'NEOLICDAFri2023071404352975nah',
											 'NEOLICDAFri2023071403340546hjr',
											 'NEOLICDAFri202307140442202iSzS',
											 'NEOLICDAFri20230714052136GCmUM',
											 'NEOLICDAFri20230714054253FynhM',
											 'NEOLICDAFri2023071405582856q5g',
											 'NEOLICDAFri20230714031556AEg8k',
											 'NEOLICDAFri202307140221172Em3M',
											 'NEOLICDAFri20230714014322CfKP6',
											 'NEOLICDAFri20230714012310Vrtr1',
											 'NEOLICDAFri202307140343205WyEX',
											 'NEOLICDAFri202307140317498b6FR',
											 'NEOLICDAFri202307140316407YLXe',
											 'NEOLICDAFri20230714032227T9Mji',
											 'NEOLICDAFri20230714043123Rih8B',
											 'NEOLICDAFri20230714042743QTDc3',
											 'NEOLICDAFri20230714052815Sdfc1',
											 'NEOLICDAFri202307140543065SgCD',
											 'NEOLICDAFri202307140556567YDz1',
											 'NEOLICDAFri20230714033642Rwz9R',
											 'NEOLICDAFri20230714040037NrKCg',
											 'NEOLICDAFri202307140430453cKF2',
											 'NEOLICDAFri202307140440467FTVk',
											 'NEOLICDAFri20230714044311MW2bH',
											 'NEOLICDAFri2023071405015761SRF',
											 'NEOLICDAFri20230714042031RG9uF',
											 'NEOLICDAFri202307140437354RkvY',
											 'NEOLICDAFri202307140509298BbQX',
											 'NEOLICDAFri202307141227575tSvj',
											 'NEOLICDAFri20230714031822U2Shd',
											 'NEOLICDAFri20230714030749PFC8m',
											 'NEOLICDAFri20230714041617LN6by',
											 'NEOLICDAFri20230714035638SxStt',
											 'NEOLICDAFri202307140506376nJF4',
											 'NEOLICDAFri202307140548225Baed',
											 'NEOLICDAFri202307140259483Von9',
											 'NEOLICDAFri20230714020014KhdtE',
											 'NEOLICDAFri20230714031914SK3M2',
											 'NEOLICDAFri20230714125110LJEr6',
											 'NEOLICDAFri2023071401343795cop',
											 'NEOLICDAFri202307140104474E3Kc',
											 'NEOLICDAFri20230714022709GTHU8',
											 'NEOLICDAFri20230714040049NakCA',
											 'NEOLICDAFri20230714041639EtYUK',
											 'NEOLICDAFri20230714042431Ajzyv',
											 'NEOLICDAFri202307140405189dr3J',
											 'NEOLICDAFri20230714042315KQiXQ',
											 'NEOLICDAFri20230714045816KfnX3',
											 'NEOLICDAFri202307140526116KVsz',
											 'NEOLICDAFri20230714041605UrwoY',
											 'NEOLICDAFri202307140440579Wgbu',
											 'NEOLICDAFri20230714045532AzsSQ',
											 'NEOLICDAFri20230714050535FvMWcr',
											 'NEOLICDAFri202307140511507VXPE',
											 'NEOLICDAFri20230714054936CAqsm',
											 'NEOLICDAFri202307140249267Xtjx',
											 'NEOLICDAFri202307140154214xEQc',
											 'NEOLICDAFri20230714032205HQmMM',
											 'NEOLICDAFri20230714040034LzDZb',
											 'NEOLICDAFri20230714034458RRXkD',
											 'NEOLICDAFri202307140458368jEMm',
											 'NEOLICDAFri20230714043045Q7xrx',
											 'NEOLICDAFri2023071404371928r7V',
											 'NEOLICDAFri20230714044612CvWwj',
											 'NEOLICDAFri202307140515195jjSp',
											 'NEOLICDAFri202307140534512CETk',
											 'NEOLICDAFri2023071405244168Nwb',
											 'NEOLICDAFri2023071405082531bsN',
											 'NEOLICDAFri20230714051602Dctoz',
											 'NEOLICDAFri20230714054914GM5Hg',
											 'NEOLICDAFri20230714022657Kodfs',
											 'NEOLICDAFri20230714023839KTf4x',
											 'NEOLICDAFri20230714025647D3r5v',
											 'NEOLICDAFri20230714025314JycqN',
											 'NEOLICDAFri202307140223155nEtF',
											 'NEOLICDAFri20230714123656He28c',
											 'NEOLICDAFri20230714010339FkZTK',
											 'NEOLICDAFri202307140323136Kf5y',
											 'NEOLICDAFri20230714040810QkdvY',
											 'NEOLICDAFri20230714030922LSGG4',
											 'NEOLICDAFri202307140401198rdse',
											 'NEOLICDAFri20230714034044DoVRa',
											 'NEOLICDAFri20230714043544JoB9d',
											 'NEOLICDAFri20230714053153922Ss',
											 'NEOLICDAFri20230714050632APnBL',
											 'NEOLICDAFri202307140507324QEHu',
											 'NEOLICDAFri202307140520363rXaq',
											 'NEOLICDAFri202307140529316jDhN',
											 'NEOLICDAFri202307140547042SEkd',
											 'NEOLICDAFri20230714054224GwJdB',
											 'NEOLICDAFri20230714021610QXajN',
											 'NEOLICDAFri20230714021942MZpnz',
											 'NEOLICDAFri20230714025046QgbRN',
											 'NEOLICDAFri20230714023510T17CC',
											 'NEOLICDAFri20230714020514x1fp4',
											 'NEOLICDAFri20230714021023FqWBr',
											 'NEOLICDAFri20230714024309E7jdA',
											 'NEOLICDAFri20230714034012J4BuN',
											 'NEOLICDAFri20230714041528UHfmM',
											 'NEOLICDAFri202307140539565bU3Z',
											 'NEOLICDAFri20230714052545PRZBC',
											 'NEOLICDAFri20230714060122AfCRM',
											 'NEOLICDAFri20230714043610UEHy5',
											 'NEOLICDAFri20230714045826VVheh',
											 'NEOLICDAFri2023071404441127bBb',
											 'NEOLICDAFri20230714011928bvppd',
											 'NEOLICDAFri20230714124619L7brM',
											 'NEOLICDAFri20230714035132SveRM',
											 'NEOLICDAFri20230714044119DoEib',
											 'NEOLICDAFri20230714043746G3YSe',
											 'NEOLICDAFri20230714051113KZbUD',
											 'NEOLICDAFri20230714015820AYDVu',
											 'NEOLICDAFri20230714032646FcVXn',
											 'NEOLICDAFri20230714032209HedyP',
											 'NEOLICDAFri20230714021326DWV8L',
											 'NEOLICDAFri202307140149337CGak',
											 'NEOLICDAFri20230714014607qbHYc',
											 'NEOLICDAFri20230714034010JWAFU',
											 'NEOLICDAFri2023071403322084T7c',
											 'NEOLICDAFri20230714054412UXgNY',
											 'NEOLICDAFri20230714054230E4894',
											 'NEOLICDAFri202307140538263CLu8',
											 'NEOLICDAFri20230714054134N8SZq');
