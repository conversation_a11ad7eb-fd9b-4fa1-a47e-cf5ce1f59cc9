-- update reRegistrationStatus as failure which got marked due to a bug
-- https://monorail.pointz.in/p/fi-app/issues/detail?id=73425

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = 'dc946568-595b-4e27-8c8d-66f172753609';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_FAIL'))
where id = 'dc946568-595b-4e27-8c8d-66f172753609';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED'))
where id = 'dc946568-595b-4e27-8c8d-66f172753609';


update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = '6380a5e5-27fe-4012-b795-493c49ac89e5';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_FAIL'))
where id = '6380a5e5-27fe-4012-b795-493c49ac89e5';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED'))
where id = '6380a5e5-27fe-4012-b795-493c49ac89e5';


update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = 'a8b5bb40-9a30-4a90-975f-ed540586e57d';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_FAIL'))
where id = 'a8b5bb40-9a30-4a90-975f-ed540586e57d';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED'))
where id = 'a8b5bb40-9a30-4a90-975f-ed540586e57d';


update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = '567320a5-741c-4740-9eff-bb7816ace093';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_FAIL'))
where id = '567320a5-741c-4740-9eff-bb7816ace093';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED'))
where id = '567320a5-741c-4740-9eff-bb7816ace093';


update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = '85775cb8-8f60-47f3-a9ee-450cee2fb18f';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_FAIL'))
where id = '85775cb8-8f60-47f3-a9ee-450cee2fb18f';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED'))
where id = '85775cb8-8f60-47f3-a9ee-450cee2fb18f';


update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = '7580526c-a463-494f-9876-40dee681490a';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_FAIL'))
where id = '7580526c-a463-494f-9876-40dee681490a';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED'))
where id = '7580526c-a463-494f-9876-40dee681490a';


update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = '88b0d251-fadd-40de-a70c-05a6412f98ca';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_FAIL'))
where id = '88b0d251-fadd-40de-a70c-05a6412f98ca';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('UPDATE_VENDOR_FAILURE_TYPE_UNSPECIFIED'))
where id = '88b0d251-fadd-40de-a70c-05a6412f98ca';
