-- CX issue Ticket No. 4504201
-- for the below nominee we have 19 July 1964 18:30:00 in the db and the actual dob is 20 July 1964
-- for wealth onboarding we are using the dob date time as it is stored in db not converting to ist
-- Nominee dob column in nominees table  has type timestamp and storing random times which is causing the issue,
-- for unblocking user quickly updating the values manually

update nominees
set dob = '1964-07-20 06:30:00.000000 +00:00',
	updated_at = NOW()
where id = 'NOM221008bAV4SdhGTgWye/+zLackZA==' and actor_id = 'AC220917as7aLWjKRSCJdqnrWP3drw==';
