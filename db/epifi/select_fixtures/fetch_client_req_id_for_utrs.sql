select orders.client_req_id, transactions.utr from orders INNER JOIN orders_transactions_map on orders.id = orders_transactions_map.order_id INNER JOIN transactions
    ON orders_transactions_map.transaction_id = transactions.id WHERE transactions.utr in ('205712509776','FBBT220575116359','205913776907','FBBT220595134939',
                                                                                         '206021173454','206023187582','FBBT220605193805','206112260673',
                                                                                         '206112267459','206112267461','206112267480','206112267481',
                                                                                         '206212474450','206213479437','206216515632','206216515892',
                                                                                         '206216516443','206216516543','206216521442','206216521433',
                                                                                         '206219560772','206313677183','206313677250','206313677300',
                                                                                         '206313677284','206313677285','206313677295','206313677334',
                                                                                         '206313677338','206313677367','206313677379','206313677357',
                                                                                         '206313677400','206313677382','FBBT220635284084','206318737223',
                                                                                         '206412869286','206412869420','206412869623','206412869668',
                                                                                         '206412869725','206413869766','206413869872','206413869891',
                                                                                         '206413870453','206413870559','206413870594','206413870684',
                                                                                         '206413870982','206413871332','206413871498','FBBT220645314163'
                                                                                         ,'FBBT220645314348','FBBT220645314360','206512128481','206512128503',
                                                                                         '206512128482','206512128504','206512128505','206512128510',
                                                                                         '206512128486','206512128487','206512128511','206512128513',
                                                                                         '206512128489','206512128515','206512128490','206512128491',
                                                                                         '206512128514','FBBT220655339392','FBBT220655339391','FBBT220655339390',
                                                                                         '206612274342','206612274324','206612274398','206612274415',
                                                                                         '206612274417','206613274508','206613274635','206613274638',
                                                                                         '206613274667','206613274756','206613274770','206613275133',
                                                                                         'FBBT220665357442','FBBT220665357415','FBBT220665357485');
