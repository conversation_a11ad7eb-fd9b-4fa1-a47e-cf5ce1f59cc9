-- removing existing address field, updating new address and remarks for existing alpaca PI --
UPDATE payment_instruments set identifier = '{ "international_account": { "name": "Alpaca Securities LLC", "actual_account_number": "1636877", "secure_account_number": "xxx6877", "bank_branch_name": "W. MonroeStreet Chicago", "bank_name": "BMO Harris Bank", "bank_address": { "regionCode": "US", "postalCode": "60603", "administrativeArea": "Illinois", "locality": "Chicago", "sublocality": "W. MonroeStreet", "addressLines": ["BMO Harris Bank NA - BMO Harris Bank 111. W. MonroeStreet"], "recipients": ["BMO Harris Bank"], "organization": "BMO Harris Bank" }, "swift_code": "HATRUS44", "routing_code": "*********", "address": { "regionCode": "US", "postalCode": "94401", "administrativeArea": "San Mateo", "locality": "California", "sublocality": "San Mateo", "addressLines": ["3 East Third Ave Suite 233 San Mateo"], "recipients": ["Alpaca Securities LLC"], "organization": "Alpaca Securities LLC" }, "funds_forwarding_account_identifier": "FFC EPFY-9064696IN" } }'
where id = 'paymentinstrument-alpaca-international-account';
