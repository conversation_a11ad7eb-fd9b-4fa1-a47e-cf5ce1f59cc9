-- https://epifi.slack.com/archives/C01NTC58Z35/p1742887052602909
update auth_factor_updates
set overall_status = 'OVERALL_STATUS_FAILED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED')),
	updated_at = now():::timestamp
where id = 'bd8f22c5-9882-443c-8013-cda476842cae';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_FAILED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED')),
	updated_at = now():::timestamp
where id = 'f8a55227-ae07-4920-9faa-8fe6cd417591';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_FAILED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED')),
	updated_at = now():::timestamp
where id = '8fa085b7-5956-43d4-b547-dc79790a51f5';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_FAILED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED')),
	updated_at = now():::timestamp
where id = 'a95b1f01-83d8-4078-80a1-882200165137';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_FAILED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED')),
	updated_at = now():::timestamp
where id = '85b23fd2-7238-4de3-9944-f22e29d51be7';
