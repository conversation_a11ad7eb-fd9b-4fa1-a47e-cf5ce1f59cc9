CREATE TABLE IF NOT EXISTS card_requests
(
	id                       STRING NOT NULL,
	card_id                  STRING NOT NULL,
	actor_id                 STRING NOT NULL,
	orch_id                  STRING NOT NULL,
	vendor                   STRING NOT NULL,
	request_details          JSONB NOT NULL,
	next_action				 JSONB NULL,
	workflow               	 STRING NOT NULL,
	status                   STRING NOT NULL,
	provenance               STRING  NOT NULL,
	created_at               TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at               TIMESTAMPTZ NOT NULL DEFAULT now(),
	deleted_at               TIMESTAMPTZ NULL,
	PRIMARY KEY(id ASC),
	INDEX card_requests_orch_id_idx (orch_id ASC),
	INDEX card_requests_card_id_idx (card_id ASC),
	FAMILY "frequently_updated" (updated_at, status, next_action),
	FAMILY "seldom_updated" (workflow, deleted_at, request_details),
	FAMILY "primary" (id, card_id, actor_id, provenance, created_at, orch_id, vendor)
);
COMMENT ON TABLE card_requests IS 'entity where we will keep track of all card requests and will store the state machine for each request';
COMMENT ON COLUMN card_requests.orch_id IS 'id for orchestrating the request at celestial/orchestrator';
COMMENT ON COLUMN card_requests.status IS 'high level status of the request';
COMMENT ON COLUMN card_requests.provenance IS 'enum denoting entry point for the request, APP/SHERLOCK etc';
COMMENT ON COLUMN card_requests.next_action IS 'deeplink of the next screen to be shown to the user when client polls';
COMMENT ON COLUMN card_requests.request_details IS 'Metadata for a given request. This can be user provided information or information collected internally for initiating the flow';

CREATE TABLE IF NOT EXISTS card_request_stages
(
	id                      STRING NOT NULL,
	card_request_id         STRING NOT NULL,
	external_request_id 	STRING NOT NULL,
	orch_id                 STRING NOT NULL,
	stage                   STRING NOT NULL,
	stage_execution_details JSONB NOT NULL,
	status                  STRING NOT NULL,
	sub_status              STRING NOT NULL,
	staled_at               TIMESTAMPTZ NULL,
	completed_at            TIMESTAMPTZ NULL,
	created_at              TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at              TIMESTAMPTZ NOT NULL DEFAULT now(),
	deleted_at              TIMESTAMPTZ NULL,
	PRIMARY KEY(id ASC),
	INDEX card_request_stage_orch_id (orch_id ASC),
	INDEX card_request_stage_card_request_id_stage_idx (card_request_id, stage ASC),
	FAMILY "frequently_updated" (stage, updated_at, status, sub_status),
	FAMILY "seldom_updated" (staled_at, stage_execution_details),
	FAMILY "primary" (id, card_request_id, external_request_id, orch_id, created_at, completed_at, deleted_at)
);
COMMENT ON TABLE card_request_stages IS 'table to store specific stage related data for card_requests';
COMMENT ON COLUMN card_request_stages.stage_execution_details IS 'metadata for the given stage';
COMMENT ON COLUMN card_request_stages.status IS 'high level status of the request stage';
COMMENT ON COLUMN card_request_stages.sub_status IS 'granular level status of the request stage';
COMMENT ON COLUMN card_request_stages.staled_at IS 'time to make step stale so that re-execution of the step can be done';
COMMENT ON COLUMN card_request_stages.completed_at IS 'time at which the request stage is completed';

CREATE TABLE IF NOT EXISTS credit_card_skus (
	sku_type                STRING NOT NULL,
	type                    STRING NOT NULL,
	feature_info            JSONB NOT NULL,
	vendor_card_sku         JSONB NOT NULL,
	created_at              TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at              TIMESTAMPTZ NOT NULL DEFAULT now(),
	deleted_at              TIMESTAMPTZ NULL,
	PRIMARY KEY (sku_type, type ASC),
	FAMILY "frequently_updated" (updated_at),
	FAMILY "seldom_updated" (feature_info, vendor_card_sku, deleted_at),
	FAMILY "primary" (sku_type, type, created_at)
);
COMMENT ON TABLE credit_card_skus IS 'table to define the card variants offered by Epifi';
COMMENT ON COLUMN credit_card_skus.feature_info IS 'card variant information like number of free cards, etc';
COMMENT ON COLUMN credit_card_skus.vendor_card_sku IS 'Vendor specific information related to the above features';
COMMENT ON COLUMN credit_card_skus.type IS 'type of card as in DEBIT/CREDIT';

CREATE TABLE IF NOT EXISTS credit_card_sku_overrides
(
	actor_id                STRING NOT NULL,
	card_sku_type           STRING NOT NULL,
	feature_override_info   JSONB NOT NULL,
	created_at              TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at              TIMESTAMPTZ NOT NULL DEFAULT now(),
	deleted_at              TIMESTAMPTZ NULL,
	PRIMARY KEY (actor_id, card_sku_type ASC),
	FAMILY "frequently_updated" (updated_at, feature_override_info),
	FAMILY "seldom_updated" (deleted_at),
	FAMILY "primary" (actor_id, card_sku_type, created_at)
);
COMMENT ON TABLE credit_card_sku_overrides IS 'table to store the special provisions for an actor like more number of card provisions, etc.';
COMMENT ON COLUMN credit_card_sku_overrides.feature_override_info IS 'blob containing the information regarding the override information specific to the actor';
