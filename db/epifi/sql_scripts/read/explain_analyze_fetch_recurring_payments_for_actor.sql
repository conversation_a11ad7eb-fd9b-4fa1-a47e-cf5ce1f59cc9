EXPLAIN ANALYZE
SELECT *
FROM recurring_payments
WHERE state != 'CREATION_QUEUED'
  AND (
	from_actor_id = 'AC210826y2kM5kypRZyduy0Ts6isXg=='
		OR (
		to_actor_id = 'AC210826y2kM5kypRZyduy0Ts6isXg=='
			AND share_to_payee = true
			AND initiated_by = 'PAYER'
			AND state != 'CREATION_INITIATED'
			AND type = 'UPI_MANDATES'
		)
		OR (
		to_actor_id = 'AC210826y2kM5kypRZyduy0Ts6isXg=='
			AND share_to_payee = true
			AND initiated_by = 'PAYEE'
		)
	)
  AND created_at <= '2024-08-29 16:45:00.000000 +00:00'::TIMESTAMPTZ
  AND state IN ('ACTIVATED', 'CREATION_INITIATED')
  AND recurring_payments.deleted_at IS NULL
ORDER BY created_at DESC
LIMIT 12;

EXPLAIN ANALYZE
SELECT *
FROM recurring_payments
WHERE state != 'CREATION_QUEUED'
  AND (
	from_actor_id = 'AC221109/8olXeRMSDm7hQ7HgB4nBA=='
		OR (
		to_actor_id = 'AC221109/8olXeRMSDm7hQ7HgB4nBA=='
			AND share_to_payee = true
			AND initiated_by = 'PAYER'
			AND state != 'CREATION_INITIATED'
			AND type = 'UPI_MANDATES'
		)
		OR (
		to_actor_id = 'AC221109/8olXeRMSDm7hQ7HgB4nBA=='
			AND share_to_payee = true
			AND initiated_by = 'PAYEE'
		)
	)
  AND created_at <= '2024-08-29 16:45:00.000000 +00:00'::TIMESTAMPTZ
  AND state IN ('ACTIVATED', 'CREATION_INITIATED')
  AND recurring_payments.deleted_at IS NULL
ORDER BY created_at DESC
LIMIT 12;
