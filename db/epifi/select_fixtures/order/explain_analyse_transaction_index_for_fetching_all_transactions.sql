-- fixture to generate the statement bundle to be shared with crdb team to debug why the query is still going for primary index join although all the columns are present in the index
explain analyse (debug )WITH TXN AS (SELECT row_number() OVER ( PARTITION BY t.order_ref_id ORDER BY t.computed_executed_at DESC) AS rn,
                    computed_executed_at,
                    id,
                    status,
                    trans_remarks,
                    payment_protocol,
                    order_ref_id,
                    partner_bank,
                    utr
             FROM transactions t
             WHERE ((t.status IN ('SUCCESS','FAILED','REVERSED','IN_PAYMENT') AND
                    (t.pi_from IN ('PI210806MUORgEWBSKuxXpwcLCgGsA==', 'PI210806XMNrC6nSQFS9MqiuuGz5xw==','PI210806rzLYGKRbSB+gxnYmFxR1qQ==') AND
                     t.computed_executed_at <= CURRENT_DATE ::TIMESTAMPTZ)) ) OR
                 ((t.status IN ('SUCCESS') AND t.pi_to IN ('PI210806MUORgEWBSKuxXpwcLCgGsA==', 'PI210806XMNrC6nSQFS9MqiuuGz5xw==','PI210806rzLYGKRbSB+gxnYmFxR1qQ==') AND t.computed_executed_at <= CURRENT_DATE ::TIMESTAMPTZ)))
SELECT *
FROM TXN
WHERE rn = 1
ORDER BY computed_executed_at ASC
	LIMIT 30 OFFSET 0;


-- fixture to generate the statement bundle to be shared with crdb team to debug why the union query is working perfectly and returning from secondary index itself
explain analyse (debug )
SELECT *
FROM ((WITH TXN AS (SELECT row_number() OVER ( PARTITION BY t.order_ref_id ORDER BY t.computed_executed_at DESC) AS rn,
						   computed_executed_at,
						   status,
						   trans_remarks,
						   payment_protocol,
						   order_ref_id,
						   partner_bank,
						   utr
					FROM transactions t
					WHERE (t.status IN ('SUCCESS', 'FAILED', 'REVERSED') AND
						   (t.pi_from IN ('PI210806MUORgEWBSKuxXpwcLCgGsA==', 'PI210806XMNrC6nSQFS9MqiuuGz5xw==','PI210806rzLYGKRbSB+gxnYmFxR1qQ==') AND
							t.computed_executed_at <= CURRENT_DATE ::TIMESTAMPTZ)))
	   SELECT *
	   FROM TXN
	   WHERE rn = 1
	   ORDER BY computed_executed_at ASC
	   LIMIT 30 OFFSET 0))
UNION
((WITH TXN AS (SELECT row_number() OVER ( PARTITION BY t.order_ref_id ORDER BY t.computed_executed_at DESC) AS rn,
					  computed_executed_at,
					  status,
					  trans_remarks,
					  payment_protocol,
					  order_ref_id,
					  partner_bank,
					  utr
			   FROM transactions t
			   WHERE (t.status IN ('SUCCESS') AND
					  (t.pi_to IN ('PI210806MUORgEWBSKuxXpwcLCgGsA==', 'PI210806XMNrC6nSQFS9MqiuuGz5xw==','PI210806rzLYGKRbSB+gxnYmFxR1qQ==') AND
					   t.computed_executed_at <= CURRENT_DATE ::TIMESTAMPTZ)))
  SELECT *
  FROM TXN
  WHERE rn = 1
  ORDER BY computed_executed_at ASC
  LIMIT 30 OFFSET 0));
