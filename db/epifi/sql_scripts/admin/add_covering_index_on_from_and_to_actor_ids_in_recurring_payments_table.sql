CREATE INDEX CONCURRENTLY IF NOT EXISTS recurring_payments_to_actor_id_covering_idx ON recurring_payments (to_actor_id ASC) STORING (from_actor_id, type, state, initiated_by, deleted_at, share_to_payee);
CREATE INDEX CONCURRENTLY IF NOT EXISTS recurring_payments_from_actor_id_covering_idx ON recurring_payments (from_actor_id ASC) STORING (to_actor_id, type, state, initiated_by, deleted_at, share_to_payee);
