CREATE TABLE if not exists device_integrity_nonces(
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	device_id STRING NOT NULL,
	manufacturer STRING NOT NULL,
	nonce STRING NOT NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX nonce_idx (nonce ASC),
	INDEX device_integrity_nonces_updated_at_idx (updated_at ASC)
	);
