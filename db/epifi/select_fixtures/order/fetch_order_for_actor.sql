-- testing the query on prod for its performance (1 order between the time range)
EXPLAIN ANALYSE(distsql)
SELECT o.id, o.updated_at
FROM orders@orders_to_actor_id_updated_at_stores_status_workflow_idx AS o
WHERE (to_actor_id = 'ACvqG8/EEYT8u2l+Zh+xqvyQ230523==' AND status IN ('PAID', 'SETTLED', 'FULFILLED'))
  AND workflow IN ('NO_OP', 'ADD_FUNDS')
  AND updated_at >= '2023-05-23 07:06:54.000' -- account creation successful timestamp
  AND updated_at <= '2023-05-23 07:10:10.000' -- first order event final updated_at field
ORDER BY updated_at ASC
OFFSET 0 LIMIT 1;

-- testing the query on prod for its performance ( >1000 orders between the time range)
EXPLAIN ANALYSE(distsql)
SELECT o.id, o.updated_at
FROM orders@orders_to_actor_id_updated_at_stores_status_workflow_idx AS o
WHERE (to_actor_id = 'AC210622KqPeekvVTOuMmZaVRMZBew==' AND status IN ('PAID', 'SETTLED', 'FULFILLED'))
  AND workflow IN ('NO_OP', 'ADD_FUNDS')
  AND updated_at >= '2021-06-01 00:00:00.000' -- account creation successful timestamp
  AND updated_at <= '2023-05-23 07:10:10.000' -- an order event final updated_at field
ORDER BY updated_at ASC
OFFSET 0 LIMIT 1;
