-- Due to a bug in prod some b2c/recurring payment transactions were updated with req info null.
-- Fetching all such transactions to update the req info with req id and run force enquiry on them

select txn.id, txn.dedupe_id, o.wf_ref_id
From transactions as txn LEFT JOIN orders as o
                                   ON txn.order_ref_id=o.id
WHERE txn.created_at = '0001-01-01 00:00:00.000000 +00:00' AND txn.payment_req_info IS NULL ;
