-- query to fetch add funds transactions whose second lef has not succeeded after 5th June

SELECT t.*
from orders o
         INNER JOIN orders_transactions_map otm on otm.order_id = o.id
         INNER JOIN transactions t on otm.transaction_id = t.id
WHERE o.workflow in ('ADD_FUNDS')
  AND o.status in ('SETTLEMENT_FAILED', 'IN_SETTLEMENT', 'MANUAL_INTERVENTION')
  AND t.pi_from = 'paymentinstrument-pool-account-federal'
  AND o.created_at >= '2021-06-05 00:00:00.000'
