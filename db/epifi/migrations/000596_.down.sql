CREATE TABLE IF NOT EXISTS agents (
						id UUID NOT NULL DEFAULT gen_random_uuid(),
						vendor_agent_id STRING NULL,
						vendor_name STRING NULL,
						agent_type STRING NULL,
						access_level STRING NOT NULL,
						phone JSONB NULL,
						email STRING NOT NULL,
						agent_name <PERSON><PERSON><PERSON><PERSON> NULL,
						status STRING NOT NULL,
						created_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
						updated_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
						CONSTRAINT "primary" PRIMARY KEY (email ASC),
						FAMILY "primary" (id, vendor_agent_id, vendor_name, agent_type, access_level, phone, email, agent_name, status, created_at, updated_at)
);
