# Baba Users: Alternate prod account

## Use Cases
- To test Wealth Builder on prod
- Test SA onboarding till device registration on prod

## Steps to Deactivate an Account

1. **Raise a PR of the fixture to deactivate & reactivate**
   - [Sample PR](https://github.com/epiFi/gamma/pull/125378) 

2. **Run deactivate fixture**
   - [<PERSON> job link](https://jenkins-prod.pointz.in/job/Scripts/job/Backend/job/Database/job/execute-dev-scripts/)

3. **Delete stale cache**
   - Run [this job](https://jenkins-prod.pointz.in/job/Scripts/job/Backend/job/Onboarding/job/Scripts/job/Script/parambuild/?BUILD_ARG_JobName=CACHE_ACTIONS&BUILD_ARG_Args1=AUTH,CACHE_STORAGE_MIN_USER,DELETE&BUILD_ARG_Args2=minUser:************) after updating field "BUILD_ARG_Args2" with the user's phone number.
   - Note: The cache for device registration is 1 hour, so in the worst case scenario, you'll have access post 1 hour.

4. **Clear app data**
   - On iOS: uninstall & reinstall
   - On Android: clear local storage or reinstall

## Steps to Reactivate Main Account

1. **Delete the new user**
   - Use DELETE_USER dev action on new account.

2. **Run the reactivate fixture**
   - Use the undo fixture raised in the PR from step 1.
