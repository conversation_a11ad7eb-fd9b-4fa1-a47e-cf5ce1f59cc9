-- slack ref: https://epifi.slack.com/archives/C031GNXERB9/p1714395532218069
-- updating recurring payment action status to success as the the corresponding payment order is in terminal
-- corresponding sample oms order id: OD230110cxqKBzKUTTOPgzsmTz3l6g== for client req id: 7319d2bb-817a-487a-a163-f128455c05b5

update recurring_payments_actions
set state = 'ACTION_SUCCESS', updated_at = NOW()
where id in  ('bc1d2c62-e341-4934-9230-61d2e840dc41',
				'9eb1406a-dfe4-4bb8-b4cb-b287309dbc11',
				'28d041ea-fa37-4695-89e0-5a82100168b2',
				'bb135e5a-de1d-493e-822f-89e434d0e0ce',
				'0b3db23f-b656-47a9-9f1d-3411630ea9f6',
				'5f9baab0-ff20-4a23-a61b-496d9defd756',
				'2b10bb89-63c4-413e-bfd6-678a93cf7249',
				'd62842e6-1b7c-422e-a709-1fdee0c53d76',
				'2f608237-8432-4520-9060-3ad5a57e80bf',
				'60ebc974-fb2a-407c-b226-ca7c96c69cee',
				'cfc903e4-5f82-4946-a708-a0de38f3a362',
				'25ce86dd-0846-4804-8b80-ff705a0775e3',
				'a0294d16-ad54-4149-aa93-1f3fbef04cfb',
				'1e895f74-a08c-40e7-bbdc-5fc14c9c9ace',
				'4bb52cb2-13d9-4c48-a618-9ddc68243150',
				'be2930bd-f395-4033-a36e-46b70da67f9b',
				'12cb5e16-a334-4e14-ae4a-0bdc70421a27',
				'6725343d-faf9-47b5-b158-3b26d336c7ee',
				'db2ae846-ab62-48fc-994f-d7bbdae3e47f',
				'aff86514-03ff-459d-abc5-a96955f3b177',
				'343e19e7-80e2-4757-ba33-727112936228',
				'78a44c4d-0609-4ea7-8e78-86b8219d977d',
				'b9061ed0-7c02-42ab-a583-c2947d71fca8',
				'4bb5cbb3-94b8-4389-bade-23b6197f77b1',
				'6ff4f4af-51e7-407e-9142-4491a4e60d35',
				'b4a3cca2-ccc0-4cb8-ad2d-b8e8c0ab19d5',
				'b0683ba6-7d7a-4fe8-a22f-48c0f7876992',
				'98c9bbd8-0409-4868-b884-42bb79c118e9',
				'b03d8921-ef2c-4a62-b077-e41dfcc044a7')

  and client_request_id in ('86ff72ab-da5e-4576-a06b-0c0f623cbd42',
						'8025b750-6e09-40ce-86cb-8f574871c838',
						'f549d3ea-d605-4761-b437-960deba266e3',
						'6759bfb2-aa86-4340-a40d-da6ac89f3849',
						'308c68b4-f834-4afe-9f77-50571b16156b',
						'12ac75c9-2104-4f1c-aa2c-21a081dc8d2c',
						'd1e76b37-20b1-45d7-aa89-2e60487437e8',
						'a5099477-7970-4d02-9ae8-d1a8a5be648e',
						'a6a5039d-5ab8-4f11-9163-1b2c0d5682c4',
						'7e321e81-e0e7-40a5-a6ad-27b7557ddf0e',
						'319baafa-19ea-486e-9e14-d7607832ddd3',
						'590ff32c-3b2f-4d37-90d9-7bdea20746e5',
						'b51c332c-c6b6-42f4-8480-3ab9670359b3',
						'5d6cee24-a6d1-495e-9842-43ba5e61e00b',
						'b44304a1-88d2-44c0-9667-31cb89253a37',
						'e300ba71-b1e6-419e-b345-88dec3af4d89',
						'c465e43d-80f0-444c-be72-888ef076db22',
						'9e196bb0-66ba-471e-8fd2-da9ab969bf7e',
						'2924294a-200c-4987-966e-b4af747f46f8',
						'07a46eb2-d140-40c0-91f3-b2ae0a6b4928',
						'a92468a6-341d-4347-8a30-7ff830d68f1a',
						'dfe17333-5fa8-4e3b-90bb-17aec1c30c03',
						'1606b77e-76df-4801-a8d5-7167f4f5bad1',
						'bffcf7be-7bc9-4f94-aa16-c296c5712311',
						'29e4cad3-7774-4bef-9b1c-6892377eed68',
						'd11f920d-878c-494a-8fa9-9a29db7960f9',
						'f7771712-dfef-4cd6-86f3-9b46c96f5396',
						'5d98df93-4ebb-401d-b9a4-3e8f351400a4',
						'39c538de-1ddf-41c1-a323-44183e91f07e');



update recurring_payments_actions
set state = 'ACTION_FAILURE', updated_at = NOW()
where id in ('d61fdb45-59a3-4b1d-ba8a-0057b15449d9') and client_request_id in ('7947e828-8343-47dd-9fb5-0deb2a9a7f84');
