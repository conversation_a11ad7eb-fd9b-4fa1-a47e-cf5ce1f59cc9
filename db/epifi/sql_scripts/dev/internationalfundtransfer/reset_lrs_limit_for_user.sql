-- updating lrs limit of actor according back to a low value after testing.
-- this value will be updated once user goes through the LRS process via placing another outward remittance order.
update lrs_checks
set lrs_consumed_limit ='{ "currency_code": "USD", "units": 50 }',
	updated_at=now()
where id='98bf0b29-9cdb-49a8-9ac7-044baf55db74';

update international_fund_transfer_checks
set lrs_consumed_limit ='{ "currency_code": "USD", "units": 52 }',
	updated_at=now()
where actor_id='AC210914Z7+1gOdtTUuFhoHMwv0RiQ==';
