-- inserting entries in lrs_checks table if entries in INITIATED state are already not present
insert into lrs_checks (
    actor_id, provenance, lrs_consumed_limit,
    lrs_checks_status, created_at, updated_at
)
select
    actor_id,
    case when is_pre_launch_interested_user = true then 'LRS_CHECK_PROVENANCE_PRE_LAUNCH' else 'LRS_CHECK_PROVENANCE_POST_ACCOUNT_OPENING' end,
    lrs_consumed_limit,  'LRS_STATUS_INITIATED',
    created_at,
    case when lrs_limit_updated_at is null then created_at else lrs_limit_updated_at end
from
    international_fund_transfer_checks
where actor_id not in (select actor_id from lrs_checks where lrs_checks_status = 'LRS_STATUS_INITIATED');
