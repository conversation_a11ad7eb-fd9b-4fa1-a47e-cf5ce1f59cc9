-- update overallStatus to success as the number got updated at bank side
-- https://monorail.pointz.in/p/fi-app/issues/detail?id=80481

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '509ee171-08d7-4287-a67e-894e6748e8e5';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '89f42877-9255-45ab-bcdc-da06d758ed7c';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '89a90ca0-a512-4ebf-adcf-c09607b3f225';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = 'dd975210-def5-44ac-983d-83274fe252e8';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = 'e998ed54-e779-437d-b1bd-d964c7cd913d';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '07ef3f61-d2b5-47bb-977a-ec0d74e2eb8f';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '379cf1f7-5c81-4c77-8e9b-23630a825825';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '7b12a7d5-7eee-4d57-92e4-5ec20e52c5b8';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '44086e35-9a29-4ead-beed-7e8d582294ac';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = 'fdd59ac9-b9b1-4e5a-a896-0c47829c72e7';
update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '44d463df-5687-4797-8592-e52a1bf3b0da';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '8b29ac7f-0666-4379-92f8-8acd07766b66';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = 'afc49e65-fb64-4ed6-bef9-db97399d834d';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '3a781a17-2e11-4447-b3ec-05f9dd3d9615';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = 'f2bbd8a4-bf0d-445d-9fc9-20776f4396a1';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '0dd62b13-3d0b-40e0-a26b-4b74026a2e32';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '1100ea01-1f97-482b-acae-137fe4d16b15';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = '5e864d9a-6794-4145-895c-226540cce027';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_COMPLETED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('COMPLETED'))
where id = 'b89c7e6a-13b2-4ad1-9397-47a5a986981e';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '509ee171-08d7-4287-a67e-894e6748e8e5';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '89f42877-9255-45ab-bcdc-da06d758ed7c';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '89a90ca0-a512-4ebf-adcf-c09607b3f225';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = 'dd975210-def5-44ac-983d-83274fe252e8';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = 'e998ed54-e779-437d-b1bd-d964c7cd913d';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '07ef3f61-d2b5-47bb-977a-ec0d74e2eb8f';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '379cf1f7-5c81-4c77-8e9b-23630a825825';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '7b12a7d5-7eee-4d57-92e4-5ec20e52c5b8';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '44086e35-9a29-4ead-beed-7e8d582294ac';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = 'fdd59ac9-b9b1-4e5a-a896-0c47829c72e7';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '44d463df-5687-4797-8592-e52a1bf3b0da';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '8b29ac7f-0666-4379-92f8-8acd07766b66';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = 'afc49e65-fb64-4ed6-bef9-db97399d834d';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '3a781a17-2e11-4447-b3ec-05f9dd3d9615';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = 'f2bbd8a4-bf0d-445d-9fc9-20776f4396a1';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '0dd62b13-3d0b-40e0-a26b-4b74026a2e32';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '1100ea01-1f97-482b-acae-137fe4d16b15';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = '5e864d9a-6794-4145-895c-226540cce027';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_SUCCESS'))
where id = 'b89c7e6a-13b2-4ad1-9397-47a5a986981e';
