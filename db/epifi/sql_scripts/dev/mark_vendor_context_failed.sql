-- https://monorail.pointz.in/p/fi-app/issues/detail?id=75502

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = 'dad90edc-f760-4717-b646-d5c936c3ec5a';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{reRegistrationStatus}', to_jsonb('REQUEST_STATUS_FAIL'))
where id = 'dad90edc-f760-4717-b646-d5c936c3ec5a';

update auth_factor_updates
set vendor_context = jsonb_set("vendor_context"::jsonb, '{failureType}', to_jsonb('REREGISTRATION_FAILED'))
where id = 'dad90edc-f760-4717-b646-d5c936c3ec5a';