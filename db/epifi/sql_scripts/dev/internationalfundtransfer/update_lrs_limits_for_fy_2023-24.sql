-- updating lrs_checks entries if lrs_limit is already present in international_fund_transfer_checks table
-- and the lrs_consumed_limit was updated in financial year 2023-24
update lrs_checks
set lrs_checks_status = 'LRS_STATUS_COMPLETED',
    lrs_consumed_limit = international_fund_transfer_checks.lrs_consumed_limit,
    updated_at = international_fund_transfer_checks.lrs_limit_updated_at
    from international_fund_transfer_checks
where
    lrs_checks.actor_id = international_fund_transfer_checks.actor_id and
    lrs_checks.lrs_checks_status = 'LRS_STATUS_INITIATED' and
    international_fund_transfer_checks.lrs_consumed_limit is not null and
    international_fund_transfer_checks.lrs_consumed_limit != '{}' and
    international_fund_transfer_checks.lrs_limit_updated_at > '2023-03-31T00:00:00+05:30';
