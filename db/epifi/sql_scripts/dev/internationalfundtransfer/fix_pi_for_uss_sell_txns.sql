-- Manually updating the pi_to for the sell order that have been completed till date.
-- This is needed because the pi being picked up was wrong.
-- Monorail: https://monorail.pointz.in/p/fi-app/issues/detail?id=45277

-- Order was executed before incoming notifications changes went live, corresponding US stocks order was automatically moved to success on 22nd March 2023
-- UPDATE public.transactions
-- set pi_to = 'PI211124M/Fryuu8QwyqCE/gj+ZM/A=='
-- where public.transactions.particulars = 'USS/SELL/DvcpZFWJG827';

-- For PI: PI210128OJIntaTfT5yw07cwUge2Og==, Actor: AC210128jHP/LhBGRp6YwZ5xPvAgbw==
-- For PI: PI210128Yocv7Bs/Q82YVR/BqXkTfQ==, Actor: AC210128yF1ys7SRRge3hMCZffvx9Q==

UPDATE public.transactions
set pi_to = 'PI210128OJIntaTfT5yw07cwUge2Og=='
where id = 'TXN0d0vddkVTuS6l0SXOrPeCQ230404==';
-- where public.transactions.particulars = 'USS/SELL/CWDMeJhtbZpi';

UPDATE public.transactions
set pi_to = 'PI210128OJIntaTfT5yw07cwUge2Og=='
where id = 'TXNqPnNkM9PQ/+d9GYjXQsQaQ230404==';
-- where public.transactions.particulars = 'USS/SELL/2Q5CjfVSiMJw';

UPDATE public.transactions
set pi_to = 'PI210128Yocv7Bs/Q82YVR/BqXkTfQ=='
where id = 'TXN29AiJIdWRk2TB8Aw5hsmgw230404==';
-- where public.transactions.particulars = 'USS/SELL/5dLN1phBwn1Z';

UPDATE public.transactions
set pi_to = 'PI210128OJIntaTfT5yw07cwUge2Og=='
where id = 'TXN+VUG9mnzSdC8i+G6Hc2t8A230404==';
-- where public.transactions.particulars = 'USS/SELL/D9KMzHXASX4U';

UPDATE public.transactions
set pi_to = 'PI210128OJIntaTfT5yw07cwUge2Og=='
where id = 'TXNnC2k277TRRqPTzSTCjgYbA230404==';
-- where public.transactions.particulars = 'USS/SELL/AVJhzw5u4o2S';

