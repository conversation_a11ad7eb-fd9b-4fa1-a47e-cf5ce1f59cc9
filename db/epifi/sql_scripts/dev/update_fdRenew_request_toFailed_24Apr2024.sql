-- Fd Renew request was failed at federal's side and they have some issue in publishing call backs
-- we need to manually fail these requests


UPDATE deposit_accounts

SET renew_info = jsonb_set(renew_info, '{renewRequestStatus}', '"RENEW_REQUEST_UNSPECIFIED"'::jsonb),
	updated_at = NOW()
where id in ('DPAabgU3rbSsOGlZveO7gj/g240323==',
            'DP9OHR0rflRCalqFGePQzGDQ230510=='
           );


UPDATE deposit_requests
SET state = 'REQUEST_FAILED',
	last_attempt = true, updated_at = NOW()
where id in ('DRJnm5dmUKSkGzRiA2WSjGgA240418==',
			 'DR++wkaX6lSAWVDihybUqTnQ240118==',
			 'DRz/xQ8YVqSkSjwpO+vA4hrA240118==',
			 'DRT6raNWyHQBCIKfbkPe4pKw240423==') and state = 'REQUEST_INITIATED';
