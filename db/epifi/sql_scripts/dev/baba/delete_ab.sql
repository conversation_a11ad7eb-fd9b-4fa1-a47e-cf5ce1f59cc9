-- deactivating main account of ad<PERSON><PERSON> bhard<PERSON>j for prod testing
update users
set deleted_at_unix = now()::int
where actor_id = 'AC210722voRQ2x7nStWQDZ0pBV9fdA==';

update device_registrations
set deleted_at_unix = 111, -- can be used as unique identifier for re-activating using delete_ab_undo.sql script
    deleted_at      = now()
where actor_id = 'AC210722voRQ2x7nStWQDZ0pBV9fdA=='
  and deleted_at_unix = 0;
