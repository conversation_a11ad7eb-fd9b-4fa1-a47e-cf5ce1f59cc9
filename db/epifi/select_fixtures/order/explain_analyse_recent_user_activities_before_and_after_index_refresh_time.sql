-- firing this query on prod just before the time when index refresh was done
EXPLAIN ANALYSE(distsql)
(select o.id,
		o.to_actor_id,
		o.from_actor_id,
		o.workflow,
		o.tags,
		o.status,
		o.updated_at
 from orders@orders_from_actor_id_updated_at_stores_status_workflow_idx as o
 where (o.from_actor_id = 'AC210806ur05GLNgQM2n7ZnCDk/f5g==')
   AND ((o.status = 'PAID' AND o.workflow NOT IN
							   ('ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD', 'ADD_FUNDS_COLLECT',
								'COLLECT_RECURRING_PAYMENT_NO_AUTH', 'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
		(o.status = 'SETTLED' AND o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
		(o.status = 'FULFILLED' AND o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
		(o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT')) OR (o.status = 'IN_PAYMENT' AND o.workflow IN
																										  ('INTERNATIONAL_FUND_TRANSFER',
																										   'P2P_FUND_TRANSFER',
																										   'P2P_COLLECT')) OR
		(o.status = 'PAYMENT_FAILED' AND
		 o.workflow IN ('P2P_FUND_TRANSFER', 'P2P_COLLECT', 'INTERNATIONAL_FUND_TRANSFER')) OR
		(o.status = 'PAYMENT_REVERSED' AND o.workflow IN ('P2P_FUND_TRANSFER', 'P2P_COLLECT')))
   AND o.updated_at <= '2023-06-15 03:39:11.000')
union
(select o.id,
		o.to_actor_id,
		o.from_actor_id,
		o.workflow,
		o.tags,
		o.status,
		o.updated_at
 from orders@orders_to_actor_id_updated_at_stores_status_workflow_idx as o
 where (o.to_actor_id = 'AC210806ur05GLNgQM2n7ZnCDk/f5g==')
   AND ((o.status = 'PAID' AND o.workflow NOT IN
							   ('ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD', 'ADD_FUNDS_COLLECT',
								'COLLECT_RECURRING_PAYMENT_NO_AUTH', 'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
		(o.status = 'SETTLED' AND o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
		(o.status = 'FULFILLED' AND o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
		(o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT')))
   AND o.updated_at <= '2023-06-15 03:39:11.000')
order by updated_at DESC
offset 0 limit 30;


-- firing this query on prod just after the index refresh was done
EXPLAIN ANALYSE(distsql)
(select o.id,
		o.to_actor_id,
		o.from_actor_id,
		o.workflow,
		o.tags,
		o.status,
		o.updated_at
 from orders@orders_from_actor_id_updated_at_stores_status_workflow_idx as o
 where (o.from_actor_id = 'AC210806ur05GLNgQM2n7ZnCDk/f5g==')
   AND ((o.status = 'PAID' AND o.workflow NOT IN
							   ('ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD', 'ADD_FUNDS_COLLECT',
								'COLLECT_RECURRING_PAYMENT_NO_AUTH', 'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
		(o.status = 'SETTLED' AND o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
		(o.status = 'FULFILLED' AND o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
		(o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT')) OR (o.status = 'IN_PAYMENT' AND o.workflow IN
																										  ('INTERNATIONAL_FUND_TRANSFER',
																										   'P2P_FUND_TRANSFER',
																										   'P2P_COLLECT')) OR
		(o.status = 'PAYMENT_FAILED' AND
		 o.workflow IN ('P2P_FUND_TRANSFER', 'P2P_COLLECT', 'INTERNATIONAL_FUND_TRANSFER')) OR
		(o.status = 'PAYMENT_REVERSED' AND o.workflow IN ('P2P_FUND_TRANSFER', 'P2P_COLLECT')))
   AND o.updated_at <= '2023-06-18 19:30:00.000')
union
(select o.id,
		o.to_actor_id,
		o.from_actor_id,
		o.workflow,
		o.tags,
		o.status,
		o.updated_at
 from orders@orders_to_actor_id_updated_at_stores_status_workflow_idx as o
 where (o.to_actor_id = 'AC210806ur05GLNgQM2n7ZnCDk/f5g==')
   AND ((o.status = 'PAID' AND o.workflow NOT IN
							   ('ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD', 'ADD_FUNDS_COLLECT',
								'COLLECT_RECURRING_PAYMENT_NO_AUTH', 'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
		(o.status = 'SETTLED' AND o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
		(o.status = 'FULFILLED' AND o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
		(o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT')))
   AND o.updated_at <= '2023-06-18 19:30:00.000')
order by updated_at DESC
offset 0 limit 30;

