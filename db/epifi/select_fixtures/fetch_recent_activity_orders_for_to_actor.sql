-- testing the query on prod for its performance for to actor index
EXPLAIN ANALYSE(distsql)
select o.id, o.to_actor_id, o.from_actor_id, o.amount, o.workflow, o.tags, o.status, o.updated_at from
	orders@orders_to_actor_id_updated_at_stores_status_workflow_idx as o where
	( o.to_actor_id = 'AC210806ur05GLNgQM2n7ZnCDk/f5g==' ) AND
	(
			( o.status = 'PAID' AND o.workflow NOT IN ('ADD_FUNDS','REWARDS_CREATE_SD','REWARDS_ADD_FUNDS_SD','ADD_FUNDS_COLLECT','COLLECT_RECURRING_PAYMENT_NO_AUTH','COLLECT_RECURRING_PAYMENT_WITH_AUTH') ) OR
			( o.status = 'SETTLED' AND o.workflow IN ('ADD_FUNDS','ADD_FUNDS_COLLECT','P2P_INVESTMENT') ) OR
			( o.status = 'FULFILLED' AND o.workflow IN ('REWARDS_CREATE_SD','REWARDS_ADD_FUNDS_SD') ) OR
			( o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT') ) )  AND
		o.updated_at<= '2023-05-31 10:57:18.379'
order by o.updated_at DESC offset 0 limit 10;
