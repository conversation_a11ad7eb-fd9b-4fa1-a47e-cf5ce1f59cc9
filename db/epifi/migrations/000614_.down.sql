CREATE TABLE IF NOT EXISTS rules
(
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	name string NOT NULL,
	version INT NOT NULL,
	description string NOT NULL,
	evaluation_method string NOT NULL,
	external_id string,
	provenance string NOT NULL,
	confidence_score INT,
	state string NOT NULL,
	assessed_entity_type string NOT NULL,
	suspect_entity string NOT NULL,
	rule_group string,
	created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	deleted_at TIMESTAMPTZ,
	PRIMARY KEY(id ASC),
	UNIQUE (name, version),
	INDEX rules_updated_at_idx (updated_at ASC),
	INDEX rules_external_id_idx (external_id ASC)
	);
COMMENT ON TABLE rules IS 'table to store risk rules related information, based on the rules alerts gets created';
COMMENT ON COLUMN rules.suspect_entity IS 'stores the suspected entity against which the alert should be created';
COMMENT ON COLUMN rules.assessed_entity_type IS 'stores the entity type which is getting assessed i.e. transactions, users, liveness';
COMMENT ON COLUMN rules.provenance IS 'stores the source of the rules i.e. rules engine, internal rules, DS rules';
COMMENT ON COLUMN rules.confidence_score IS 'stores the confidence score of the rules, alerts generated with high
    score will have high confidence of a entity being fraud';
COMMENT ON COLUMN rules.external_id IS 'stores the id of a rule in external rule engine';
COMMENT ON COLUMN rules.evaluation_method IS 'stores the evaluation method of a rule i.e. conditional or heuristic';
COMMENT ON COLUMN rules.state IS 'stores the state of rule i.e. inactive, in-review, active etc';
COMMENT ON COLUMN rules.rule_group IS 'stores the rule_group of the rule it can be atm, crypto, aml etc';
