-- Ensure 'actor_name', 'verified_name' and 'name in bank account details' corresponds
-- to a value which we want to display to the user in receipts, transactions etc.

-- assuming we will have different account in case we start doing p2p international fund transfer

-- Adding actors pool accounts for outward remittance for US stocks
UPSERT
INTO actors(id, type, entity_id, name, ownership)
VALUES ('actor-alpaca', 'EXTERNAL_USER', null, 'US Stocks', 'US_STOCKS_ALPACA');

-- Adding pi for US STOCKS OUTWARD REMITTANCE; to which we need to send money for buy

UPSERT INTO public.payment_instruments (id, type, verified_name, identifier, state, capabilities, spam_count, issuer_classification, ownership) VALUES
('paymentinstrument-alpaca-international-account', 'INTERNATIONAL_ACCOUNT', 'US Stocks', '{ "international_account": { "name": "Alpaca Securities LLC", "address": "3 East Third Ave Suite 233 San Mateo CA 94401 USA", "actual_account_number": "1636877", "secure_account_number": "xxx6877", "bank_branch_name": "W. MonroeStreet Chicago", "bank_name": "BMO Harris Bank", "bank_address": { "regionCode": "US", "postalCode": "60603", "administrativeArea": "Illinois", "locality": "Chicago", "sublocality": "W. MonroeStreet", "addressLines": [ "BMO Harris Bank NA - BMO Harris Bank 111. W. MonroeStreet" ], "recipients": ["BMO Harris Bank"], "organization": "BMO Harris Bank" }, "swift_code": "HATRUS44", "routing_code": "*********" } }', 'CREATED', '{"INBOUND_TXN": true, "OUTBOUND_TXN": false}', 0, 'INTERNAL', 'US_STOCKS_ALPACA');
