CREATE TABLE IF NOT EXISTS txn_category_ontologies (
													   ontology_id STRING NOT NULL,
													   l0 STRING NULL,
													   l1 STRING NULL,
													   l2 STRING NULL,
													   l3 STRING NULL,
													   display_category STRING NOT NULL,
													   created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT txn_category_ontologies_pkey PRIMARY KEY (ontology_id ASC),
	INDEX txn_category_ontologies_updated_at_idx (updated_at ASC),
	INDEX txn_category_ontologies_display_category_idx (display_category ASC) WHERE deleted_at IS NULL,
	INDEX txn_category_ontologies_ontology_id_idx (ontology_id ASC) WHERE deleted_at IS NULL,
	INDEX txn_category_ontologies_l0_idx (l0 ASC)
	);

CREATE TABLE IF NOT EXISTS txn_categories (
											  id STRING NOT NULL,
											  txn_id STRING NOT NULL,
											  actor_id STRING NOT NULL,
											  ontology_id STRING NOT NULL REFERENCES txn_category_ontologies (ontology_id),
	confidence_score DECIMAL(4,2) NULL,
	provenance STRING NOT NULL,
	ds_categorisation_time TIMESTAMPTZ NULL,
	model_version STRING NULL,
	is_display_enabled BOOL NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	data_channel STRING NOT NULL,
	categorisation_source STRING NULL,
	CONSTRAINT txn_categories_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX txn_categories_txn_id_actor_id_ontology_id_provenance_key (txn_id ASC, actor_id ASC, ontology_id ASC, provenance ASC) WHERE deleted_at IS NULL,
	INDEX txn_categories_ontology_id_idx (ontology_id ASC),
	INDEX txn_categories_updated_at_idx (updated_at DESC),
	INDEX txn_categories_actor_id_updated_at_data_channel_provenance_is_display_enabled_idx (actor_id ASC, updated_at DESC, data_channel ASC, provenance ASC, is_display_enabled ASC) WHERE deleted_at IS NULL,
	FAMILY "primary" (id, txn_id, actor_id, ontology_id, confidence_score, provenance, ds_categorisation_time, model_version, created_at, deleted_at, data_channel, categorisation_source),
	FAMILY sheldom_updated (updated_at, is_display_enabled)
	);

CREATE TABLE IF NOT EXISTS user_display_categories (
													   display_category STRING NOT NULL,
													   ontology_id STRING NOT NULL REFERENCES txn_category_ontologies (ontology_id),
	category_type STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (display_category ASC)
	);

CREATE TABLE IF NOT EXISTS gplace_type_to_ontology_id_mappings (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	gplace_type STRING NOT NULL,
	ontology_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX gplace_type_to_ontology_id_mappings_gplace_type_idx (gplace_type ASC),
	INDEX gplace_type_to_ontology_id_mappings_updated_at_idx (updated_at ASC)
	);
COMMENT ON TABLE gplace_type_to_ontology_id_mappings IS 'stores gplace type to ontology_id mapping';

CREATE TABLE IF NOT EXISTS merchant_pi_gplace_fi_ontologies (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	pi_id STRING NOT NULL,
	merchant_dirty_name STRING NULL,
	latitude DECIMAL(8,4) NULL,
	longitude DECIMAL(8,4) NULL,
	fi_ontology_ids STRING[] NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX merchant_pi_gplace_fi_ontologies_pi_id_unique_idx (pi_id ASC),
	INDEX merchant_pi_gplace_fi_ontologies_merchant_name_location_idx (merchant_dirty_name ASC, latitude ASC, longitude ASC),
	INDEX merchant_pi_gplace_fi_ontologies_updated_at_idx (updated_at ASC)
	);
COMMENT ON TABLE merchant_pi_gplace_fi_ontologies IS 'use to cache pi to fi ontology ids to save on the gplace api cost';

CREATE TABLE IF NOT EXISTS crowd_aggregated_categories (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	pi_id STRING NOT NULL,
	accounting_entry STRING NOT NULL,
	ontology_ids JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT crowd_aggregated_categories_pkey PRIMARY KEY (pi_id ASC, id ASC),
	UNIQUE INDEX crowd_aggregated_categories_pi_id_accounting_entry_key (pi_id ASC, accounting_entry ASC) WHERE deleted_at IS NULL,
	INDEX crowd_aggregated_categories_updated_at_idx (updated_at DESC),
	FAMILY "primary" (id, pi_id, accounting_entry, ontology_ids, created_at),
	FAMILY seldom_updated (updated_at, deleted_at)
	);
COMMENT ON TABLE public.crowd_aggregated_categories IS 'crowd_aggregated_categories stores list of categories learnt from the user re-categorisations against a given pi_id and accounting_entry';
COMMENT ON COLUMN public.crowd_aggregated_categories.pi_id IS '{"proto_type":"categorizer.CrowdAggregatedCategory.pi_id", "comment": "crowd aggregated categories are calculated for this payment instrument id and accounting entry", "ref": "api/categorizer/crowd_aggregation_category.proto"}';
COMMENT ON COLUMN public.crowd_aggregated_categories.accounting_entry IS '{"proto_type":"categorizer.CrowdAggregatedCategory.accounting_entry", "comment": "crowd aggregated categories are for this payment instrument id and accounting entry", "ref": "api/categorizer/crowd_aggregation_category.proto"}';
COMMENT ON COLUMN public.crowd_aggregated_categories.ontology_ids IS '{"proto_type":"categorizer.CrowdAggregatedCategory.ontology_ids", "comment": "ontology ids aggregated using the user re-categorisations against the given pi_id and accounting_entry", "ref": "api/categorizer/crowd_aggregation_category.proto"}';

CREATE TABLE IF NOT EXISTS user_future_txn_categories (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	pi_id STRING NOT NULL,
	accounting_entry_type STRING NOT NULL,
	display_category STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX user_future_txn_categories_actor_id_pi_id_idx (actor_id ASC, pi_id ASC) WHERE deleted_at IS NULL,
	INDEX user_future_txn_categories_updated_at_idx (updated_at ASC)
	);
COMMENT ON TABLE public.user_future_txn_categories IS 'stores users future similar txn (txns to same PI) categories';
