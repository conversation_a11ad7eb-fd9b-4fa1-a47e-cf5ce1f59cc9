-- slack ref: https://epifi.slack.com/archives/C031GNXERB9/p1714395532218069
-- updating recurring payment action status to success as the the corresponding payment order is in terminal

update recurring_payments_actions
set state = 'ACTION_SUCCESS', updated_at = NOW()
where id in    ('bbc358ff-4d3d-46a6-9f0b-19ec45d33450',
				'72da964b-900e-420c-af2f-73ba0edc94f5',
				'960568b5-3524-49d5-be43-44523efe86d6',
				'b71bdaf8-557b-474f-a906-6b0e7eb37b0a',
				'74f9a852-469e-49ec-9f2c-bb58349c089d',
				'a086b142-129c-4432-a4aa-50fe3e920191',
				'a3c4c0d6-43ba-4903-b617-da79165891c1',
				'5fd19e00-0aa8-48ca-b885-0f8247d37223',
				'9c21dcb1-4743-481f-a5e6-d04e75e1f272',
				'373b2a74-1e15-44e5-9abd-48fd0d9305e0',
				'e1cf239a-fced-40ee-8f5e-d2ac246f72c2',
				'889652bf-1137-4edc-8eca-34838fa34668',
				'a0e96e78-3779-48c7-9596-c3962b1f0783',
				'ef1f460e-0b79-4bf1-b1bb-b82940f1f413',
				'a2a09337-7509-4369-ac8d-825c09eb0639')

  and client_request_id in ('9910e8ec-2a0f-407b-893a-a309bbe764f6',
							'dc821bef-1208-4caf-ad6b-9eef93d80eb5',
							'016e1e69-5e08-4efd-8389-b2cb84babaea',
							'7be45808-a3f7-4e2a-abca-6f82997868c3',
							'41f4a63a-0aaa-4ee4-9c97-f5dd9e44df55',
							'2e8e8c8b-eb9d-4102-9298-b849ba450766',
							'c337a356-437a-495a-8f99-8150eff1aa53',
							'7af3e523-9b36-46c9-ad53-2f87dea6c628',
							'8d8692ca-4171-4abb-8686-7d1d3c332ad0',
							'2b262f46-f466-4e81-a9dc-deb5268fe45b',
							'a97c7760-d4d0-4aad-8eb7-989987cb0009',
							'4f7ab08d-084a-4925-9de8-29b9ab90c974',
							'fcb578e3-d89c-4b4a-9c7d-3c4a118736e3',
							'855f44be-8a68-4b04-9e44-98365685d554',
							'66eae0ec-4438-4807-9f97-aefda9fd6525');
