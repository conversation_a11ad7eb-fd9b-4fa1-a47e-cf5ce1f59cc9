-- firing this query in prod env to check the performance using different indexes
explain analyse (distsql)SELECT status,created_at
FROM ((SELECT  status,created_at
	   FROM orders@orders_to_actor_id_created_at_status_idx
	   WHERE to_actor_id = 'AC220107cgeCfdkTTA+55KHkFqZz/w=='
		 AND status IN
			 ('PAYMENT_FAILED', 'COLLECT_DISMISSED_BY_PAYER', 'COLLECT_DISMISSED_BY_PAYEE', 'COLLECT_FAILED', 'EXPIRED',
			  'MANUAL_INTERVENTION')
		 AND created_at >= '2022-11-19 13:56:34.054'
		 AND created_at <= '2022-12-19 13:56:34.054'
	   ORDER BY created_at ASC
	   offset 0 limit 10)
	  UNION
	  (SELECT  status,created_at
	  FROM orders@orders_from_actor_id_created_at_status_idx
	  WHERE from_actor_id = 'AC220107cgeCfdkTTA+55KHkFqZz/w=='
		AND status IN
			('PAYMENT_FAILED', 'COLLECT_DISMISSED_BY_PAYER', 'COLLECT_DISMISSED_BY_PAYEE', 'COLLECT_FAILED', 'EXPIRED',
			 'MANUAL_INTERVENTION')
		AND created_at >= '2022-11-19 13:56:34.054'
		AND created_at <= '2022-12-19 13:56:34.054'
	  ORDER BY created_at ASC
	  offset 0 limit 10))
ORDER BY created_at ASC
offset 0 limit 10;
