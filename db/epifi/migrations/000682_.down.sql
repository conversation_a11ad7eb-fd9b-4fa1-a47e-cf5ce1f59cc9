-- dropping existing computed column for international account
ALTER TABLE payment_instruments DROP COLUMN IF EXISTS computed_unique_international_account;
ALTER TABLE payment_instruments ADD COLUMN IF NOT EXISTS computed_unique_international_account STRING NULL AS (CASE WHEN (((identifier->'international_account')->>'actual_account_number') IS NOT NULL) AND (((identifier->'international_account')->>'swift_code') IS NOT NULL) THEN concat((identifier->'international_account')->>'actual_account_number', '_', (identifier->'international_account')->>'swift_code') ELSE NULL END) STORED;
ALTER TABLE payment_instruments ADD CONSTRAINT payment_instruments_computed_unique_international_account_key UNIQUE (computed_unique_international_account);
