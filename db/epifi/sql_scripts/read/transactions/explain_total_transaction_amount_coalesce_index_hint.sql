-- Original sub-optimal query in explain_total_transaction_amount.sql file

-- Same as query in explain_total_transaction_amount_coalesce.sql with index hint to use transactions_pi_from_created_at_status_idx index.
EXPLAIN ANALYZE SELECT sum(computed_amount) FROM transactions@transactions_pi_from_created_at_status_idx
WHERE (pi_from in ('PIlBBad/+OTSimlrSEQLsLdA240214==','PI220102OJ8beN2QTmqlG9pELvz0ng==','PI220102f/xkTYkmQtuEw1YTy0jbmA==','PI220102CbgNc63TQCKb8Ty+X2OS8Q=='))
AND status IN ('SUCCESS','UNKNOWN','MANUAL_INTERVENTION','IN_PROGRESS','INITIATED')
AND (COALESCE(debited_at, credited_at, created_at) >= '2025-01-29 00:00:00' ::TIMESTAMPTZ)
AND created_at >= '2025-01-29 00:00:00'
AND (COALESCE(debited_at, credited_at, created_at) <= '2025-01-31 00:00:00' ::TIMESTAMPTZ)
AND created_at <= '2025-01-31 00:00:00'
AND payment_protocol IN ('INTRA_BANK','NEFT','IMPS','RTGS','UPI','CARD','AEPS','SWIFT','ENACH','CTS');
