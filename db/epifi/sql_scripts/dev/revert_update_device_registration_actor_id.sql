-- reverting the actor id to the existing one
update device_registrations
set actor_id = split_part(actor_id, '.', 1)
where actor_id IN (
                   'ACNvw6J7xERQOaBI1D5rVBNQ230616==.temp',
                   'ACG661/6cfTWesSlhKX3C3Cw230418==.temp',
                   'ACCRzSIHZ3QHisxrP4R6gbxg230807==.temp',
                   'AC230203rkV1caRTQYGpaDuiXrdRiA==.temp',
                   'ACeIT4jNgbTcGuuj85s+fnrA230715==.temp',
                   'ACrvOY5e9wRAKkkhbmp3pUhQ230509==.temp',
                   'ACkpBw8cDNQ/6QXHif7KcI7Q230727==.temp'
    )