-- To manually verify the deposit account maturity amount calculation at our end, we need data from pre-closed SDs of multiple durations

-- Preclosed FDs with monthly payout
select id, cast(cast((updated_at - created_at) as float4) / 3600 as float4) as active_hours
from deposit_accounts
where type = 'FIXED_DEPOSIT'
  and state = 'PRECLOSED'
  and scheme_code = 'FD_MONTHLY_INTEREST'
order by active_hours DESC limit 10;

-- Preclosed FDs with quarterly payout
select id, cast(cast((updated_at - created_at) as float4) / 3600 as float4) as active_hours
from deposit_accounts
where type = 'FIXED_DEPOSIT'
  and state = 'PRECLOSED'
  and scheme_code = 'FD_QUARTERLY_INTEREST'
order by active_hours DESC limit 10;

-- Preclosed SDs active for 1-2 days with amount > 25k
-- select id, actor_id, cast(cast((updated_at - created_at) as float4) / 3600 as float4) as active_hours
-- from deposit_accounts
-- where type = 'SMART_DEPOSIT'
--   and state = 'PRECLOSED'
--   and cast(cast((updated_at - created_at) as int) / 3600 as int) between 24 and 48
--   and cast(principal_amount ->> 'units' as int) >= 25000
-- order by active_hours DESC
-- limit 10;
--
-- -- SDs which matured and were not preclosed with tenure 10-60 days
-- select id, actor_id
-- from deposit_accounts
-- where type = 'SMART_DEPOSIT'
--   and state = 'CLOSED'
--   and cast(cast((updated_at - created_at) as int) / 3600 as int) between 240 and 1440
--   and cast(principal_amount ->> 'units' as int) >= 3000
-- order by created_at
-- limit 10;
--
-- -- SDs which matured and were not preclosed with tenure > 90 days
-- select id, actor_id
-- from deposit_accounts
-- where type = 'SMART_DEPOSIT'
--   and state = 'CLOSED'
--   and cast(cast((updated_at - created_at) as int) / 3600 as int) >= 2160
--   and cast(principal_amount ->> 'units' as int) >= 3000
-- order by created_at
-- limit 10;

-- Preclosed SDs active for 10-60 days
-- select id, actor_id, cast(cast((updated_at - created_at) as float4) / 3600 as float4) as active_hours
-- from deposit_accounts
-- where type = 'SMART_DEPOSIT'
--   and state = 'PRECLOSED'
--   and cast(cast((updated_at - created_at) as int) / 3600 as int) between 240 and 1440
-- order by active_hours DESC
-- limit 10;

-- Preclosed SDs active for > 90 days
-- select id, actor_id, cast(cast((updated_at - created_at) as float4) / 3600 as float4) as active_hours
-- from deposit_accounts
-- where type = 'SMART_DEPOSIT'
--   and state = 'PRECLOSED'
--   and cast(cast((updated_at - created_at) as int) / 3600 as int) >= 2160
-- order by active_hours DESC
-- limit 10;
