-- Updating upi pin set status for a user. His UPI PIN is in ETB_PIN_SET state but due to some issue in client side
-- we are unable to change the pin set status to PIN_SET. Updating it manually to unblock user.
UPDATE upi_account_infos
SET pin_set_state='PIN_SET',
	updated_at = NOW()
WHERE account_id='SV2+cVaXx6TKWOin1cQjiX3A230506=='
  AND pin_set_state='ETB_PIN_SET';

UPDATE upi_accounts
SET pin_set_status= 'UPI_PIN_SET_STATUS_PIN_SET',
	updated_at = NOW()
where id= '43cc8b1d-a59f-4ba2-b0da-79b7d1bcd3eb'
  AND pin_set_status='UPI_PIN_SET_STATUS_ETB_PIN_SET';
