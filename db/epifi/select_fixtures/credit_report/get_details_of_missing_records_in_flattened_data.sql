with credit_reports_filtered as (
	select * from credit_reports where id in ('f25e66c7-e82a-40bd-bb5c-17c659444787','71e1a4a7-8dd3-48a5-92dc-a2c57c24b6c3','83fcc39d-0e7f-4e81-94e2-0508aed80fd9','8ddb349b-c6bd-4b2a-926e-9a574baa72fc','bd9164be-069d-4836-bf4c-3e035caae52b')
),
onboarding_details_filterd as (
 select * from onboarding_details where actor_id in (select credit_reports_filtered.actor_id from credit_reports_filtered)
)
select
	credit_reports_filtered.credit_report_data,
	credit_reports_filtered.credit_report_data_raw,
	credit_reports_filtered.computed_is_data_purged,
	credit_reports_filtered.purged_at,
	onboarding_details_filterd.stage_details,
	onboarding_details_filterd.current_onboarding_stage,
	onboarding_details_filterd.created_at,
	onboarding_details_filterd.completed_at,
	onboarding_details_filterd.deleted_at
from onboarding_details_filterd inner join credit_reports_filtered on credit_reports_filtered.actor_id = onboarding_details_filterd.actor_id;
