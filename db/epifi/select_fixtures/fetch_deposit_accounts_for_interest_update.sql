-- get a list of all deposit accounts which were created on between 22nd Feb 2022 and 4th Match 2022.
-- This is needed as federal updated interest rates without informing in advance, and CX needs to callout to all such users.
-- select da.id,
-- 	   da.account_number,
-- 	   u.computed_phone_number as phone_number,
-- 	   da.name,
--        da.term,
-- 	   da.interest_rate,
-- 	   da.operative_account_number
-- from deposit_accounts as da
-- 		 INNER JOIN actors as a on da.actor_id = a.id
-- 		 INNER JOIN users as u on a.entity_id = u.id
-- where da.type = 'FIXED_DEPOSIT'
--   and da.term->>'months' = '16'
--   and (da.created_at between '2022-02-22'::date and '2022-03-04'::date)
-- order by da.created_at;

-- Fetch deposit accounts created by rewards to be sent to partner bank
-- Monorail: https://monorail.pointz.in/p/fi-app/issues/detail?id=20060
-- Requester: @krithika
select da.account_number,
	   u.fed_customer_info ->> 'Id' as customer_id
from deposit_accounts as da
		 INNER JOIN actors as a
					on da.actor_id = a.id
		 INNER JOIN users as u on a.entity_id = u.id
where da.type = 'SMART_DEPOSIT'
and da.provenance = 'REWARDS_APP'
order by da.created_at DESC
limit 100;
