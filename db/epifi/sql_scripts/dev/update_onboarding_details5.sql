-- Updating intentSelectionMetadata to null to put user in loan flow
-- https://monorail.pointz.in/p/fi-app/issues/detail?id=97487

UPDATE onboarding_details
SET stage_metadata = jsonb_set(stage_metadata, '{intentSelectionMetadata}', '{}'), updated_at=NOW()
WHERE actor_id in (
				   'AC36r3AwTjU9250426',
				   'AC3nYub7oUAT250428',
				   'ACiivmJrcR7b250426',
				   'ACXVJ1gpQEVt250428',
				   'ACLTTV1wy8jy250425',
				   'ACGJsVvtP22c250426',
				   'AC43yqbSyv9r250425',
				   'AC46PPcEZEru250427',
				   'AC3pXNi1d5tz250428',
				   'ACwt2gtboLZQ250425',
				   'AC3LBhhWA9vb250426',
				   'AC3rpfawzLCu250427',
				   'AC2oQQaRhqjc250426',
				   'AC2NL6e23nWs250426',
				   'AC2bmDVHuUCh250426',
				   'ACStxmsuJUjE250425',
				   'AC2eJDgyCZ6G250428',
				   'ACK2mxeYS6Tb250425',
				   'AC2bNAXjAXvt250426',
				   'AC3FsJb1RA7a250425',
				   'ACrFy6eepupi250426',
				   'AC2hgxjRAsma250425',
				   'ACwVnPdAg3pK250428',
				   'AC2xx2aNTDuQ250428',
				   'AC45XyanXRRe250425',
				   'AC21JvBMESBh250425',
				   'ACoMVqSG12yk250429',
				   'AC2kbP7WM3tw250426',
				   'AC2XQryMqv3U250428',
				   'AC325QnHanwR250425',
				   'AC3Ff3nsAbos250426',
				   'AC2pjBx9vZib250429',
				   'AC464XCp9vYe250428',
				   'AC37NemYALTj250426',
				   'ACf8t2p7Lig7250426',
				   'AC3iCLGgjGMB250427',
				   'AC233uyp2DBG250426',
				   'AC2qAV31kyCW250428',
				   'AC2xXpHnAtbU250426',
				   'ACAbmscRWWNx250425',
				   'ACL97J21cMTd250425',
				   'AC2Xz6kUH9Um250427',
				   'AC2Jhem3CWZA250425',
				   'AC2QUzuhXWsh250425',
				   'AC2aqWTpvtbp250428',
				   'AC3rG9t3Dq5e250428',
				   'AC3x9Qk2x6Bz250425',
				   'ACphY5MWoo1G250429',
				   'ACAEvm3qexSz250429',
				   'ACSpsNQzCWdz250429',
				   'AC288s6PMd69250426',
				   'AC2US6jR1dst250425',
				   'AC3QM2TyY19B250425',
				   'ACTA2KdSNbdq250428',
				   'AC2EJQrKSfVw250426',
				   'AC3ahyzw4nip250428',
				   'ACyXYPDfkbht250427',
				   'ACqBrujudPXY250425',
				   'ACSXRBHw47Hy250425',
				   'AC37eyhHwqMs250426',
				   'AC3qys6STcR5250428',
				   'AC2M7d9mFnx4250428',
				   'AC32mNyHGS8i250427',
				   'ACEhL11HvYV2250426',
				   'AC2Dg5naR4pc250428',
				   'AC366nxP54zN250426',
				   'ACi45UKWiu8H250425',
				   'ACvayb2cHsqy250425',
				   'AC46bWpyECkA250425',
				   'AC3FSLoBEsG3250426',
				   'AC44xTNV8REw250425',
				   'AC4EGsLnYisJ250428',
				   'ACEYnH5Hgzpv250425',
				   'AC3MYHbW83gZ250428',
				   'AC47HzgX5TZ5250427',
				   'ACB2GFRXvesv250426',
				   'AC5RZr5JGeq9250425',
				   'AC3fxPT1Pynt250425',
				   'AC9YUEVNnUJQ250425',
				   'AC42ZMashcA7250428',
				   'AC3M4EZDXkek250425',
				   'AC2y2WdKvEmQ250426',
				   'ACdQZJ71pEEx250428',
				   'AC2k7z9T2kqs250427',
				   'AC31iJxpvv4d250427',
				   'ACi7GeKSuFgp250428',
				   'AC3eEiXMt18o250425',
				   'ACUYpbyBZA5v250428',
				   'AC4FkbGnLMhe250425',
				   'AC4BCBjvZSRH250425',
				   'ACrUp8FHQK9E250426',
				   'AC3LYz2TZsKg250428',
				   'AC2iFc4YwAFx250429',
				   'AC5CkCPXKCDk250426'
	);
