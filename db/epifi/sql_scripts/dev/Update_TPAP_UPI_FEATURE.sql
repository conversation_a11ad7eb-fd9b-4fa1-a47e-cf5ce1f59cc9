UPDATE onboarding_details
SET feature_details = CASE
						  WHEN feature_details IS NULL THEN
							  jsonb_build_object(
								  'featureInfo', jsonb_build_object(
								  'FEATURE_UPI_TPAP', jsonb_build_object(
									  'completed_at', CURRENT_TIMESTAMP,
									  'featureStatus', 'FEATURE_STATUS_ACTIVE'
													  )
												 )
							  )
						  ELSE
							  jsonb_set(
								  feature_details,
								  ARRAY['featureInfo', 'FEATURE_UPI_TPAP']::text[],
								  jsonb_build_object(
									  'completed_at', CURRENT_TIMESTAMP,
									  'featureStatus', 'FEATURE_STATUS_ACTIVE'
								  )
							  )
	END
WHERE actor_id IN (
	SELECT actor_id
	FROM upi_accounts
	WHERE id IN (
				 'ffa1012b-e497-4500-b4fc-4956277d82b5',
				 'fa568a67-1179-4aea-81aa-a722727a4b48',
				 'fee98f2b-0e4c-4747-8bb3-da837f9b4eca',
				 '5729dc96-24ee-4ba2-8127-e2aa7bf7ed2b',
				 '1aa7f175-4329-40c3-9309-41de113cb8f8',
				 '36aa8663-7272-4152-9a32-8bdf9021cc3a',
				 'fa34c417-2d91-4ab4-8526-7c559136eb70',
				 '0650c556-08c8-40f4-a42d-dfcd54547701',
				 '50f4a8fc-996d-445a-bb71-616b3cc62581',
				 '65a6966b-2785-41e4-b93f-28cef55674b7',
				 '83ec5ef7-8767-4323-850f-4b8e873600ce',
				 '3d55c8b1-43d3-4ff6-8909-92b3290a4161',
				 'cd1a7f41-15ec-4b0d-ab38-69b42b1c4e29',
				 'd638be3f-79c2-44c3-9d4f-b704cbe0aea0',
				 'd075a826-649e-4ab6-83c1-3fb451e09469',
				 '5e539521-dade-41ab-9a2a-3db2a9bc15ac',
				 'bd96a895-d15f-422b-8822-288e5b16ea48',
				 'f0eb143d-dbc7-47c9-803e-1941cf8ee44d',
				 '9d9fbf45-860c-403f-b168-4a2eefa1dae4',
				 'e47ac6c3-af95-4d37-b2a4-a7921c245cd1',
				 '6cc566af-5bea-44ae-81a3-088e81cde04a',
				 '5779ce69-62e8-4fc5-8ae4-e8f297824d43',
				 '9cd6c699-1ccc-49a2-9d3e-827a4de4d41a',
				 'a89688d3-c24c-44d6-a0ba-34672091df71',
				 'b137ac41-1576-4423-9788-b4a49ba95de3',
				 '2fddad67-9ca3-4a1a-aa28-aff87ec4eff7',
				 '117ac79c-8b64-4479-b153-89892c9fcabc',
				 'e5dcb18b-13bf-4b0f-a29c-9d85a8c835e9',
				 '738532a0-c970-4758-ba4f-63d650eac97e',
				 '20567f35-ad6b-4fe9-8530-fd08b8dca86c',
				 'abe24d23-ad6b-4820-abaf-1f857f133c82',
				 'e25fd2df-eef0-4be0-b08e-8f79c9ab5e3c',
				 'ca2ca40f-6d96-410d-9b63-e8a627c32c27',
				 '42cc1678-a904-491d-b6ec-af47eae413f9',
				 'c121c52e-e251-4d76-9ff2-696b4909c063',
				 '67219b43-c18c-41af-86f2-2e8235b3e60d',
				 '23b76758-ad08-45e5-94b0-96fc40989ed2',
				 '9ce8b3db-2920-4aec-bfe3-ea09542024a4')
)
  AND (feature_details IS NULL
	OR NOT feature_details->'featureInfo' ? 'FEATURE_UPI_TPAP');
