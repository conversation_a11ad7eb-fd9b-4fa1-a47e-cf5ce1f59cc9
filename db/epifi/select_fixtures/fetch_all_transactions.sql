--  query to fetch all all transactions for given actor and pi ids
--  running the query in prod to debug why some transactions are missing for the given actor
--  for more ref https://epifi.slack.com/archives/C01LF3YTETX/p1676872003546429
WITH ORDER_WITH_TXN AS (SELECT row_number()    OVER ( PARTITION BY otm.order_id ORDER BY t.created_at DESC) AS rn, o.id,
							   t.created_at AS txn_created_at,
							   t.credited_at,
							   t.debited_at,
							   t.id         AS txn_id,
							   t.order_ref_id,
							   t.payment_protocol,
							   t.pi_from,
							   t.pi_to,
							   t.updated_at AS txn_updated_at
						FROM orders_transactions_map otm
								 INNER JOIN transactions t ON otm.transaction_id = t.id
								 INNER JOIN orders o ON o.id = otm.order_id
						WHERE ((t.pi_from IN ('PI220315/tLbKOb1SHunFNp/fXD5Lg==', 'PI220315aF/ZEHEfSgqqAGbGeF0csA==',
											  'PI220315cyuO3fuPSU6Anms2E5t7GA==') AND
								COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
								'2023-02-16 17:20:54' ::TIMESTAMPTZ) OR
							   (t.pi_to IN ('PI220315/tLbKOb1SHunFNp/fXD5Lg==', 'PI220315aF/ZEHEfSgqqAGbGeF0csA==',
											'PI220315cyuO3fuPSU6Anms2E5t7GA==') AND
								COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
								'2023-02-16 17:20:54' ::TIMESTAMPTZ))
						  AND ((o.from_actor_id = 'AC220212HSWLyyVkT2iQcYDDqgBXew==') OR
							   (o.to_actor_id = 'AC220212HSWLyyVkT2iQcYDDqgBXew=='))
						  AND ((o.status = 'PAID' AND o.workflow NOT IN
													  ('ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD',
													   'ADD_FUNDS_COLLECT', 'COLLECT_RECURRING_PAYMENT_NO_AUTH',
													   'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
							   (o.status = 'SETTLED' AND
								o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
							   (o.status = 'FULFILLED' AND
								o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
							   (o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT'))))
SELECT *
FROM ORDER_WITH_TXN
WHERE rn = 1
ORDER BY COALESCE(debited_at, credited_at, txn_updated_at) DESC LIMIT 30
OFFSET 1;
