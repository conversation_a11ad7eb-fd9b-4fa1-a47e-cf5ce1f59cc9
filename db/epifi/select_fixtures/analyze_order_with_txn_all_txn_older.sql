-- explain analyse older all transactions query
EXPLAIN ANALYSE (distsql) WITH ORDER_WITH_TXN AS (SELECT row_number() OVER ( PARTITION BY otm.order_id ORDER BY t.created_at DESC) AS rn,
							   o.id,
							   t.updated_at                                                              AS txn_updated_at,
							   t.credited_at,
							   t.created_at                                                              AS txn_created_at,
							   t.id                                                                      AS txn_id,
							   t.debited_at
						FROM orders_transactions_map otm
								 INNER JOIN transactions t ON otm.transaction_id = t.id
								 INNER JOIN orders o ON o.id = otm.order_id
						WHERE ((t.pi_from IN ('PI210806MUORgEWBSKuxXpwcLCgGsA==', 'PI210806XMNrC6nSQFS9MqiuuGz5xw==', 'PI210806rzLYGKRbSB+gxnYmFxR1qQ==') AND COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
																  '2022-08-25 22:59:11.011' ::TIMESTAMPTZ) OR
							   (t.pi_to IN ('PI210806MUORgEWBSKuxXpwcLCgGsA==', 'PI210806XMNrC6nSQFS9MqiuuGz5xw==', 'PI210806rzLYGKRbSB+gxnYmFxR1qQ==') AND COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
																'2022-08-25 22:59:11.011' ::TIMESTAMPTZ))
						  AND ((o.from_actor_id = 'AC210806ur05GLNgQM2n7ZnCDk/f5g==') OR (o.to_actor_id = 'AC210806ur05GLNgQM2n7ZnCDk/f5g=='))
						  AND ((o.status = 'PAID' AND o.workflow NOT IN
													  ('ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD',
													   'ADD_FUNDS_COLLECT', 'COLLECT_RECURRING_PAYMENT_NO_AUTH',
													   'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
							   (o.status = 'SETTLED' AND
								o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
							   (o.status = 'FULFILLED' AND
								o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
							   (o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT'))))
SELECT *
FROM ORDER_WITH_TXN
WHERE rn = 1
ORDER BY COALESCE(debited_at, credited_at, txn_updated_at) DESC
LIMIT 30 OFFSET 0;

-- explain analyse query with two table join
EXPLAIN ANALYSE (distsql) WITH ORDER_WITH_TXN AS (SELECT row_number() OVER ( PARTITION BY o.id ORDER BY t.created_at DESC) AS rn,
							   o.id,
							   t.updated_at                                                              AS txn_updated_at,
							   t.credited_at,
							   t.created_at                                                              AS txn_created_at,
							   t.id                                                                      AS txn_id,
							   t.debited_at
						FROM orders o
								 INNER JOIN transactions t ON t.order_ref_id = o.id
						WHERE ((t.pi_from IN ('PI210806MUORgEWBSKuxXpwcLCgGsA==', 'PI210806XMNrC6nSQFS9MqiuuGz5xw==', 'PI210806rzLYGKRbSB+gxnYmFxR1qQ==') AND COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
																  '2022-08-25 22:59:11.011' ::TIMESTAMPTZ) OR
							   (t.pi_to IN ('PI210806MUORgEWBSKuxXpwcLCgGsA==', 'PI210806XMNrC6nSQFS9MqiuuGz5xw==', 'PI210806rzLYGKRbSB+gxnYmFxR1qQ==') AND COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
																'2022-08-25 22:59:11.011' ::TIMESTAMPTZ))
						  AND ((o.from_actor_id = 'AC210806ur05GLNgQM2n7ZnCDk/f5g==') OR (o.to_actor_id = 'AC210806ur05GLNgQM2n7ZnCDk/f5g=='))
						  AND ((o.status = 'PAID' AND o.workflow NOT IN
													  ('ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD',
													   'ADD_FUNDS_COLLECT', 'COLLECT_RECURRING_PAYMENT_NO_AUTH',
													   'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
							   (o.status = 'SETTLED' AND
								o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
							   (o.status = 'FULFILLED' AND
								o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
							   (o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT'))))
SELECT *
FROM ORDER_WITH_TXN
WHERE rn = 1
ORDER BY COALESCE(debited_at, credited_at, txn_updated_at) DESC
LIMIT 30 OFFSET 0;
