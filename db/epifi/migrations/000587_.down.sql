ALTER TABLE auth_factor_updates
	RENAME COLUMN computed_request_id TO computed_request_id2;

ALTER TABLE IF EXISTS auth_factor_updates
	ADD COLUMN IF NOT EXISTS computed_request_id STRING NULL FAMILY update_vendor
	AS (CASE WHEN ((vendor_context->>'requestId') IS NOT NULL) THEN (vendor_context->>'requestId') ELSE NULL END) STORED;

CREATE UNIQUE INDEX IF NOT EXISTS afu_unique_computed_request_id ON public.auth_factor_updates(computed_request_id);
