-- select all the pending transactions without serialID for actor `AC201229d8yLnpwASUSN3wNkpQVB+A==`
SELECT id
FROM transactions
WHERE (pi_from IN
       ('PI201229K8GDSjiGTfiRMGa0Ga8d0A==', 'PI201229sjACAMePQy25sp8MIrrN0Q==', 'PI201229xX5RbfjqQmy6pb1sFduQUQ==',
        'PI210111CyGhsfajQzK5uIPXShfeyQ==', 'PI210305KwBynwkKRVSopUVNZZ4SKQ==', 'PI210312lX1i6GgrSQatNdR68Uvowg==',
        'PI210316kwIEUYMvTWiZQRkWnIW1sw==', 'PI210324T7jE/rI8Qne5uhE1XcPkkA==', 'PI210326f+IBozJwQeiUETSgTteaMA==',
        'PI210405HruGV7vsRp+iGIze4l27yQ==', 'PI210417NdbzsLj0SpmODnaciWPWyA==', 'PI210628hKJcybljTYe+BUDCveiEJw==') OR
       pi_to IN
       ('PI201229K8GDSjiGTfiRMGa0Ga8d0A==', 'PI201229sjACAMePQy25sp8MIrrN0Q==', 'PI201229xX5RbfjqQmy6pb1sFduQUQ==',
        'PI210111CyGhsfajQzK5uIPXShfeyQ==', 'PI210305KwBynwkKRVSopUVNZZ4SKQ==', 'PI210312lX1i6GgrSQatNdR68Uvowg==',
        'PI210316kwIEUYMvTWiZQRkWnIW1sw==', 'PI210324T7jE/rI8Qne5uhE1XcPkkA==', 'PI210326f+IBozJwQeiUETSgTteaMA==',
        'PI210405HruGV7vsRp+iGIze4l27yQ==', 'PI210417NdbzsLj0SpmODnaciWPWyA==', 'PI210628hKJcybljTYe+BUDCveiEJw=='))
  AND (array_length(regexp_split_to_array(utr, ':'), 1) = 2 -- filter cases <date>:cbsID
    OR (array_length(regexp_split_to_array(utr, ':'), 1) = 3 AND
        regexp_split_to_array(utr, ':')[1] = 'EXTENDED')) -- filter cases EXTENDED:<date>:cbsID
  AND partner_bank = 'FEDERAL_BANK';
