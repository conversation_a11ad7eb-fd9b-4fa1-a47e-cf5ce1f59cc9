
-- this is the query behind GetByUtrWithTransactionsV1 dao method
-- made this fixture to test the query in the prod environment

-- audit history
-- utr - 1:31102022:FB934
SELECT "orders"."id","orders"."external_id","orders"."from_actor_id","orders"."to_actor_id","orders"."workflow","orders"."status","orders"."provenance","orders"."expire_at","orders"."tags","orders"."ui_entry_point","orders"."client_req_id","orders"."created_at","orders"."updated_at","orders"."deleted_at","orders"."wf_ref_id" FROM "orders" INNER JOIN transactions ON orders.id = transactions.order_ref_id WHERE transactions.utr = '1:31102022:FB934' AND "orders"."deleted_at" IS NULL
