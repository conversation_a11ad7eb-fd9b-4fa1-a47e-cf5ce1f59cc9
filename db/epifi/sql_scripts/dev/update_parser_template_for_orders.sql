-- This fixture is to update parser template for given order ids to process DC_forex_refund for these transactions
-- For these transactions, dc_forex entry is marked as capped incorrectly due to bank side changes in the procedure to charge forex fee.
-- These transaction got parsed with GENERIC parser since, parser changes were WIP during that time and seems like these cases got missed in recon as well which happened post FI side changes went to prod.

UPDATE transactions
SET metadata = jsonb_set(
	metadata,
	'{parser_template,id}',
	'"PARSER_TEMPLATE_ID_DC_FOREX_FEE_TRANSACTION"',
	false
			   ),
	updated_at = now()

WHERE metadata -> 'parser_template' ->> 'id' = 'PARSER_TEMPLATE_ID_GENERIC'
and order_ref_id in (
					 'ODir4wSttJQBGnN3UarQYDMg240826==',
					 'ODowghzMiFQ/OjBZ7bpqrGEQ240824==',
					 'OD1xSvI6BeTSegbjcPk+RLkQ240827==',
					 'ODPiiQQCQTTsulwtlQ8+XqjQ240824==',
					 'ODInQeJU/zQRu6N3yw97p9KQ240826==',
					 'ODYAI+8emyRFK2H2THYc48dg240824=='
	);


