ALTER TABLE IF EXISTS actor_pi_resolutions
ADD CONSTRAINT IF NOT EXISTS fk_pi_from_ref_payment_instruments FOREIGN KEY pi_from REFERENCES payment_instruments(id);

ALTER TABLE IF EXISTS actor_pi_resolutions
ADD CONSTRAINT IF NOT EXISTS fk_pi_to_ref_payment_instruments FOREIGN KEY pi_to REFERENCES payment_instruments(id);

ALTER TABLE IF EXISTS transactions
ADD CONSTRAINT IF NOT EXISTS fk_pi_from_ref_payment_instruments FOREIGN KEY pi_from REFERENCES payment_instruments(id);

ALTER TABLE IF EXISTS transactions
ADD CONSTRAINT IF NOT EXISTS fk_pi_to_ref_payment_instruments FOREIGN KEY pi_to REFERENCES payment_instruments(id);

ALTER TABLE IF EXISTS card_merchant_infos
ADD CONSTRAINT IF NOT EXISTS fk_pi_id_ref_payment_instruments FOREIGN KEY pi_id REFERENCES payment_instruments(id);
