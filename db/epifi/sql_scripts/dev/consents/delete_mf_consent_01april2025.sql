-- CX Issue, user wants to delete/stop tracking all their MF analyser data
-- slack ref: https://epifi.slack.com/archives/C02G1AVJVFD/p1742472404352199
-- monorail: https://monorail.pointz.in/p/fi-app/issues/detail?id=96256
update consents
set deleted_at = now(), updated_at = now()
where actor_id = 'ACikNZ/PbySoGexzbNsqYXVg240529=='
  and consent_type in ('FI_WEALTH_MF_HOLDINGS_IMPORT_MF_CENTRAL_TNC', 'FI_WEALTH_MF_HOLDINGS_IMPORT_MF_CENTRAL_TNC_V2', 'MF_HOLDINGS_DATA_AVAILABILITY_FOR_LIMITED_TIME')
  and deleted_at is null;
