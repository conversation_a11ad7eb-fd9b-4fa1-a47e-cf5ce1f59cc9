CREATE TABLE credit_report_download_attempts (
	 id STRING NOT NULL,
	 credit_report_download_id STRING NOT NULL,
	 actor_id STRING NOT NULL,
	 orch_id STRING NOT NULL,
	 vendor STRING NOT NULL,
	 status STRING NOT NULL,
	 sub_status STRING NOT NULL,
	 completed_at TIMESTAMPTZ NULL,
	 created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
	 updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
	 deleted_at TIMESTAMPTZ NULL,
	 CONSTRAINT "primary" PRIMARY KEY (id ASC),
	 INDEX credit_report_download_attempts_updated_at_idx (updated_at DESC)
);
comment on table credit_report_download_attempts is 'table to store the multiple credit report download attempts for a user';
comment on column credit_report_download_attempts.credit_report_download_id is 'credit report download id against which this attempt is being made';
