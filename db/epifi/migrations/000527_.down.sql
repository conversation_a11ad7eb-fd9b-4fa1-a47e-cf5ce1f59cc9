DROP TABLE IF EXISTS service_requests;

CREATE TABLE IF NOT EXISTS alfred_requests
(
	id           STRING      NOT NULL,
	actor_id     STRING      NOT NULL,
	request_type STRING      NOT NULL,
	status       STRING      NOT NULL,
	details      jsonb       NOT NULL,
	created_at   TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at   TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at   TIMESTAMPTZ NULL,
	PRIMARY KEY (actor_id ASC, id),
	UNIQUE INDEX alfred_requests_actor_request_type_idx (actor_id, request_type, deleted_at),
	UNIQUE INDEX alfred_requests_id_idx (id),
	INDEX alfred_requests_actor_request_type_updated_at_idx (actor_id, request_type, created_at),
	INDEX alfred_requests_updated_at_idx (updated_at)
);

COMMENT ON TABLE alfred_requests IS 'Table to keep track of all requests made by the users';
COMMENT ON COLUMN alfred_requests.request_type IS 'Type of request made by the user'

