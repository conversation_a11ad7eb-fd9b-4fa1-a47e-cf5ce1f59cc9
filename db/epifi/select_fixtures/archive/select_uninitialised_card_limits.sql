select card_id from card_limits WHERE NOT card_limit_data->'cardLimitDetails' @> '[{"currentAllowedAmount": {"currencyCode": "INR", "units": "1"}, "locType": "DOMESTIC", "maxAllowedAmount": {"currencyCode": "INR", "units": "100000"}, "txnType": "ATM"}, {"currentAllowedAmount": {"currencyCode": "INR", "units": "1"}, "locType": "INTERNATIONAL", "maxAllowedAmount": {"currencyCode": "INR", "units": "100000"}, "txnType": "ATM"}, {"currentAllowedAmount": {"currencyCode": "INR", "units": "1"}, "locType": "DOMESTIC", "maxAllowedAmount": {"currencyCode": "INR", "units": "500000"}, "txnType": "POS"}, {"currentAllowedAmount": {"currencyCode": "INR", "units": "1"}, "locType": "INTERNATIONAL", "maxAllowedAmount": {"currencyCode": "INR", "units": "500000"}, "txnType": "POS"}]'::jsonb;
