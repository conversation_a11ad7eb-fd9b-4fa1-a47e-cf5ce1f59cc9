-- inserting lrs checks entries for financial year 2022-23 by using international_fund_transfer_checks
insert into lrs_checks (
    actor_id, provenance, lrs_consumed_limit,
    lrs_checks_status, created_at, updated_at
)
select
    actor_id,
    case when is_pre_launch_interested_user = true then 'LRS_CHECK_PROVENANCE_PRE_LAUNCH' else 'LRS_CHECK_PROVENANCE_BUY_FLOW' end,
    last_fy_lrs_consumed_limit,
    'LRS_STATUS_COMPLETED',
    created_at, '2023-03-31T00:00:00+05:30'
from
    international_fund_transfer_checks
where last_fy_lrs_consumed_limit is not null and last_fy_lrs_consumed_limit != '{}';
