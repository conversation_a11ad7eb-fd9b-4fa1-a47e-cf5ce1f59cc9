CREATE TABLE IF NOT EXISTS action
(
	id             UUID        	   NOT NULL DEFAULT gen_random_uuid(),
	case_id        INT64		   NOT NULL,
	review_type    STRING,
	action_type    STRING,
	parameters     JSONB,
	source 		   STRING,
	analyst_email  STRING,
	initiated_at   TIMESTAMPTZ,
	created_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
	updated_at     TIMESTAMPTZ NOT NULL DEFAULT now(),
	CONSTRAINT 	   "primary" PRIMARY KEY (case_id, id),
	UNIQUE 		   (id),

	-- Contains columns which are updated rarely
	FAMILY "seldom_updated" (parameters),
	-- Contains column which are never updated after insertion
	FAMILY "primary" (id, case_id, review_type, action_type, source, analyst_email, initiated_at, created_at, updated_at)
	);

COMMENT ON TABLE action IS 'risk-case-management: This table will contain logs of all the actions against a case';
COMMENT ON COLUMN action.review_type IS '"proto_type":"risk.case_store.enums", "comment":"type of review for which action was taken", "ref":"api.risk.case_store.enums.proto"';
COMMENT ON COLUMN action.action_type IS '"proto_type":"risk.case_management.review.enums", "comment":"action type enum  which particular action was taken", "ref":"api.risk.case_management.review.enums.proto"';
COMMENT ON COLUMN action.parameters IS '"proto_type":"risk.case_management.review.action", "comment":"for each action type we will have some action specific parameters, "ref":"api.risk.case_management.review.action.proto"';
COMMENT ON COLUMN action.source IS '"proto_type":"risk.case_management.review.enums", "comment":"identify from where the action was taken", "ref":"api.risk.case_management.review.enums.proto"';
COMMENT ON COLUMN action.analyst_email IS 'email of analyst taking the action';
COMMENT ON COLUMN action.initiated_at IS 'time at which action was initiated from the source system';

create index action_updated_at_index
	on action (updated_at);
