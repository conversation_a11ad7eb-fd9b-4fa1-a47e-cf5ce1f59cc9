
-- using index updated_at but filtering on updated_at and sorting on created_at and firing query used in GetOrdersByStatusAndWorkflowFilter dao method to check the performance of the query for DEBIT transaction
-- slack thread: https://epifi.slack.com/archives/C019ALXRXTP/p1695714620856889

explain
analyze (distsql ) (select o.id, o.to_actor_id, o.from_actor_id, o.workflow, o.tags, o.status, o.updated_at, o.created_at from orders@orders_from_actor_id_updated_at_stores_status_workflow_idx as o where ( o.from_actor_id = 'AC210806ur05GLNgQM2n7ZnCDk/f5g==' )
AND (( o.status = 'PAID' AND o.workflow NOT IN ('ADD_FUNDS','REWARDS_CREATE_SD','REWARDS_ADD_FUNDS_SD','ADD_FUNDS_COLLECT','COLLECT_RECURRING_PAYMENT_NO_AUTH',
'COLLECT_RECURRING_PAYMENT_WITH_AUTH') ) OR ( o.status = 'SETTLED' AND o.workflow IN ('ADD_FUNDS','ADD_FUNDS_COLLECT','P2P_INVESTMENT') ) OR
( o.status = 'FULFILLED' AND o.workflow IN ('REWARDS_CREATE_SD','REWARDS_ADD_FUNDS_SD') ) OR ( o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT') ) OR
( o.status = 'IN_PAYMENT' AND o.workflow IN ('INTERNATIONAL_FUND_TRANSFER','P2P_FUND_TRANSFER','P2P_COLLECT') ) OR ( o.status = 'PAYMENT_FAILED' AND o.workflow IN
('P2P_FUND_TRANSFER','P2P_COLLECT','INTERNATIONAL_FUND_TRANSFER') ) OR ( o.status = 'PAYMENT_REVERSED' AND o.workflow IN ('P2P_FUND_TRANSFER','P2P_COLLECT') ) )  AND
updated_at <= '2023-10-24 19:40:19.403' order by o.updated_at DESC offset 0 limit 30);

-- using index updated_at but filtering and sorting on created_at and firing query used in GetOrdersByStatusAndWorkflowFilter dao method to check the performance of the query for DEBIT transaction
-- slack thread: https://epifi.slack.com/archives/C019ALXRXTP/p1695714620856889
explain
analyze (distsql ) (select o.id, o.to_actor_id, o.from_actor_id, o.workflow, o.tags, o.status, o.updated_at, o.created_at from orders@orders_from_actor_id_created_at_stored_idx as o where ( o.from_actor_id = 'AC210806ur05GLNgQM2n7ZnCDk/f5g==' )
AND (( o.status = 'PAID' AND o.workflow NOT IN ('ADD_FUNDS','REWARDS_CREATE_SD','REWARDS_ADD_FUNDS_SD','ADD_FUNDS_COLLECT','COLLECT_RECURRING_PAYMENT_NO_AUTH',
'COLLECT_RECURRING_PAYMENT_WITH_AUTH') ) OR ( o.status = 'SETTLED' AND o.workflow IN ('ADD_FUNDS','ADD_FUNDS_COLLECT','P2P_INVESTMENT') ) OR
( o.status = 'FULFILLED' AND o.workflow IN ('REWARDS_CREATE_SD','REWARDS_ADD_FUNDS_SD') ) OR ( o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT') ) OR
( o.status = 'IN_PAYMENT' AND o.workflow IN ('INTERNATIONAL_FUND_TRANSFER','P2P_FUND_TRANSFER','P2P_COLLECT') ) OR ( o.status = 'PAYMENT_FAILED' AND o.workflow IN
('P2P_FUND_TRANSFER','P2P_COLLECT','INTERNATIONAL_FUND_TRANSFER') ) OR ( o.status = 'PAYMENT_REVERSED' AND o.workflow IN ('P2P_FUND_TRANSFER','P2P_COLLECT') ) )  AND
created_at <= '2023-10-24 19:40:19.403' order by o.created_at DESC offset 0 limit 30);
