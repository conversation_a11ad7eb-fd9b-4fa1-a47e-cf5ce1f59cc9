-- reading account with corrupted fiCreationSucceededAt for a specific account
-- 'SV2101047xyH0ViZSCS8BKcxYXJu9A==' is corrupted
-- 'SV2105048RXwLEg2Q6GmIgbFkMNnrw==' has original value
select * from savings_accounts where (account_creation_info->>'fiCreationSucceededAt') ~ '\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.?\d*$' and id IN('SV2101047xyH0ViZSCS8BKcxYXJu9A==', 'SV2105048RXwLEg2Q6GmIgbFkMNnrw==');
