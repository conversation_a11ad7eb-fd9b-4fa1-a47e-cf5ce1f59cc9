-- query to fetch all transactions for given PI ids and time upper bound
-- firing this query on prod to debug why a particular user is not able to see all transactions on app .
-- https://monorail.pointz.in/p/fi-app/issues/detail?id=28779&q=owner%3Ame%20and%20m110&can=2

WITH TXN AS (SELECT row_number() OVER ( PARTITION BY t.order_ref_id ORDER BY t.created_at DESC) AS rn,
					id,
					updated_at,
					debited_at,
					credited_at,
					order_ref_id
			 FROM transactions t
			 WHERE (((t.pi_from IN ('PI210413J6/YYZDxRym6C5ox3HheOw==', 'PI210413nvqMdO3NSySnb07VXxvRxw==',
									'PI2104134lDcuW+5RNy6e1rW2Cu3JA==', 'PI210421h4pOHHxFSVmP9U0eI6w4zg==',
									'PI2108057qda0JnISRyW0vlzJuhH4w==') AND
					  COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
					  '2022-09-19T15:15:08.48671272Z' ::TIMESTAMPTZ) OR
					 (t.pi_to IN ('PI210413J6/YYZDxRym6C5ox3HheOw==', 'PI210413nvqMdO3NSySnb07VXxvRxw==',
								  'PI2104134lDcuW+5RNy6e1rW2Cu3JA==', 'PI210421h4pOHHxFSVmP9U0eI6w4zg==',
								  'PI2108057qda0JnISRyW0vlzJuhH4w==') AND
					  COALESCE(t.debited_at, t.credited_at, t.updated_at) <=
					  '2022-09-19T15:15:08.48671272Z' ::TIMESTAMPTZ))))
SELECT *
FROM TXN
WHERE rn = 1
ORDER BY COALESCE(debited_at, credited_at, updated_at) DESC
LIMIT 30 OFFSET 0;

select id
from orders as o
WHERE (((o.id in
		 ('*********/4kUsNET9GF1LiUfKWPLQ==', 'OD220919SdH7S1kjQ8m/9IeM0q+wQA==', 'OD220919zQ/zPg9lRraRsAq9zze5Uw==',
		  'OD220918wZDndvsiRwWtVZ1K3ormkA==', 'OD220918yGj6/xNLS4aAY5spMrUZbw==', 'OD220918C9/6da1WTOeUOk2/iSnWRw==',
		  'OD220917RR0Os30lR2qZcf7J1iDLJQ==', 'OD220917lki0A0UbTyGiBmeR8ZQVvQ==', 'OD220916mpnnnDgBQ8Kknc1K9Sz3bQ==',
		  'OD220916/gc/tSd0RXuePexDfGbggw==', 'OD220916eSum1WvYSzqvVWOAyn7i0Q==', 'OD220916+Z+05DqTSMmHDm0Ubqj6kA==',
		  'OD220915fIRTFhSKQ9a7h7VCezH5bg==', 'OD22091595RV9SaKQ8GNMa1lB1ozDw==', 'OD220915OFP6+YfhT5+r7P6xYLUrDA==',
		  'OD220914N/y0DBBWSgGw/9evioWvGw==', 'OD220914RdL5b5YtS22jLjslbN0UOg==', 'OD220914Ywuo6N8JRu6s8xc2HqC+OA==',
		  'OD220914rsuyOG5zSh2grDRJmOy5TQ==', 'OD220913EusOFkCKTqGMppaWoF8XoA==', 'OD220912G1E3bFpzQUm1YBpgh1ROhQ==',
		  'OD220912XfytIqs7RcmqJPzDIdQr3w==', 'OD22091298l8GJ+CSMORvsFcM57NHA==', 'OD220911WpI9XuMPTAWeU1FUvIgvcg==',
		  'OD220911edwWLEXLTbu5ezFGKvTkrw==', 'OD220911SoPBibC9Sl2Og4Nxg+OZUA==', 'OD220911PRAHfF+pQaScqexFqnXwPw==',
		  'OD220911x/NXhDXyTDCG++o3mfJC7g==', 'OD220911unDhh1vGQDezHThfG4Xr5w==',
		  'OD220911D5Zd78aYQNmUtWP0Mo9C0Q==')) and ((o.from_actor_id = 'AC210413eHj5Toi5Quy+wUJIodSLxA==') OR
													(o.to_actor_id = 'AC210413eHj5Toi5Quy+wUJIodSLxA=='))) AND
	   ((o.status = 'PAID' AND o.workflow NOT IN
							   ('PAID', 'ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD', 'ADD_FUNDS_COLLECT',
								'COLLECT_RECURRING_PAYMENT_NO_AUTH', 'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
		(o.status = 'SETTLED' AND o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
		(o.status = 'FULFILLED' AND o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
		(o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT'))));

WITH TXN AS (SELECT row_number() OVER ( PARTITION BY t.order_ref_id ORDER BY t.created_at DESC) AS rn,
					id,
					updated_at,
					debited_at,
					credited_at,
					order_ref_id
			 FROM transactions t
			 WHERE (((t.pi_from IN ('PI210413J6/YYZDxRym6C5ox3HheOw==', 'PI210413nvqMdO3NSySnb07VXxvRxw==',
									'PI2104134lDcuW+5RNy6e1rW2Cu3JA==', 'PI210421h4pOHHxFSVmP9U0eI6w4zg==',
									'PI2108057qda0JnISRyW0vlzJuhH4w==') AND
					  COALESCE(t.debited_at, t.credited_at, t.updated_at) <= '2022-09-11T04:32:26Z' ::TIMESTAMPTZ) OR
					 (t.pi_to IN ('PI210413J6/YYZDxRym6C5ox3HheOw==', 'PI210413nvqMdO3NSySnb07VXxvRxw==',
								  'PI2104134lDcuW+5RNy6e1rW2Cu3JA==', 'PI210421h4pOHHxFSVmP9U0eI6w4zg==',
								  'PI2108057qda0JnISRyW0vlzJuhH4w==') AND
					  COALESCE(t.debited_at, t.credited_at, t.updated_at) <= '2022-09-11T04:32:26Z' ::TIMESTAMPTZ))))
SELECT *
FROM TXN
WHERE rn = 1
ORDER BY COALESCE(debited_at, credited_at, updated_at) DESC
LIMIT 30 OFFSET 1;

select id
from orders as o
WHERE (((o.id in
		 ('OD220911KEQwD6ZiQLmsjwQI8bYs+A==', 'OD220910YwNWjM7YQnOnpXLtUUnfgQ==', 'OD220910ziVLnEbqRI2QvjKTT5sp0A==',
		  'OD220910fzkyldfBQ/iVcWqh7YU66w==', 'OD2209099bu5UlqjT1uUls9J0mxY2Q==', 'OD220909zXRosAIVRfimbOQCDitT4Q==',
		  'OD220909CyY33apIRf+Ne1qS1E/AfA==', 'OD220909X6NMqubOT1OD37Ya6QQ68w==', 'OD220908A23OBs+LS1KFQHakSxcK5Q==',
		  'OD2209071qqqL4NRRsKTRT+8NNf0hA==', 'OD220907EXRpNyAwT4WJHJ/oZ91B9A==', 'OD220906vdLEV2IORQWx7tTSNOBNNA==',
		  'OD220906u2OJtvpwTxelLlwx7mG9BA==', 'OD220906pDlBYuIVTOOIs7VrjS7Awg==', 'OD220905mCmMKD3nQ7enAAH3wIw/hQ==',
		  'OD220905gE0zJEfoQai4v8ipy/U96Q==', 'OD22090588Yt/aHRS26Iyv7nt7v5/Q==', 'OD220904HZl4pZk/TlidhOUIOJPdzg==',
		  'OD220904anVAghCOTrCu6gb6dBYJgw==', 'OD220904sYSgblGUScyMdVP8q5WDIw==', 'OD220904ptrbVWw7TOunrKbVpI4rpA==',
		  'OD220903DK7SjREjS16rrBbXv5Qpfg==', 'OD220903ZpJqbwrdR4u0zNMi0W5voA==', 'OD220903nm7d3KrKSrWcrPgxpj7U6A==',
		  'OD220903fPGNApIERYe7/3tWtDAj6w==', 'OD220902ace6MWErTVSG2Po3e+5b0g==', 'OD220902As4NThDuQ0CiH6drD1lFKA==',
		  'OD220902/scAA4oeSrWoI12t4FeS6g==', 'OD220903IPyfe6jCR1C/ToQaPQynXA==',
		  'OD220902oBcmGuUlQVyMMAqoBN5zWw==')) and ((o.from_actor_id = 'AC210413eHj5Toi5Quy+wUJIodSLxA==') OR
													(o.to_actor_id = 'AC210413eHj5Toi5Quy+wUJIodSLxA=='))) AND
	   ((o.status = 'PAID' AND o.workflow NOT IN
							   ('PAID', 'ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD', 'ADD_FUNDS_COLLECT',
								'COLLECT_RECURRING_PAYMENT_NO_AUTH', 'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
		(o.status = 'SETTLED' AND o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
		(o.status = 'FULFILLED' AND o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
		(o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT'))));


WITH TXN AS (SELECT row_number() OVER ( PARTITION BY t.order_ref_id ORDER BY t.created_at DESC) AS rn,
					id,
					updated_at,
					debited_at,
					credited_at,
					order_ref_id
			 FROM transactions t
			 WHERE (((t.pi_from IN ('PI210413J6/YYZDxRym6C5ox3HheOw==', 'PI210413nvqMdO3NSySnb07VXxvRxw==',
									'PI2104134lDcuW+5RNy6e1rW2Cu3JA==', 'PI210421h4pOHHxFSVmP9U0eI6w4zg==',
									'PI2108057qda0JnISRyW0vlzJuhH4w==') AND
					  COALESCE(t.debited_at, t.credited_at, t.updated_at) <= '2022-09-02T11:49:10Z' ::TIMESTAMPTZ) OR
					 (t.pi_to IN ('PI210413J6/YYZDxRym6C5ox3HheOw==', 'PI210413nvqMdO3NSySnb07VXxvRxw==',
								  'PI2104134lDcuW+5RNy6e1rW2Cu3JA==', 'PI210421h4pOHHxFSVmP9U0eI6w4zg==',
								  'PI2108057qda0JnISRyW0vlzJuhH4w==') AND
					  COALESCE(t.debited_at, t.credited_at, t.updated_at) <= '2022-09-02T11:49:10Z' ::TIMESTAMPTZ))))
SELECT *
FROM TXN
WHERE rn = 1
ORDER BY COALESCE(debited_at, credited_at, updated_at) DESC
LIMIT 30 OFFSET 1;

select id
from orders as o
WHERE (((o.id in
		 ('OD220902qBm4zlp5QAyTl9hCm/I5rQ==', 'OD220902kSMjMhvMTo65xXUprDKHDA==', 'OD220902yGiyougbQNuBpIApgcz6bQ==',
		  'OD220902vpO80rmeQWKPGD/dTv6dwA==', 'OD220902wtQ0Unz3Q3WOsPBeHzp3KA==', 'OD220901+UeBg2TNT0GzB9xybnLBEg==',
		  'OD220901eBx+M73+Q22beT5tDeeHzA==', 'OD220901SiF7TEw7TJyNeMDYeL7xoA==', 'OD220901d1XyrZsiRYSJ4oNIjnS1Kg==',
		  'OD220901bMhiG0mLSk6c67HhP7jXtA==', 'OD220901DcDmQX3bTJOec2KJDTfzZQ==', 'OD220831PbXlJRG5RyORkpSZjZaGog==',
		  'OD2208303MDPoL35SxGtc8lBl4iAww==', 'OD220830K0dCBzsqQuKA0Rvbkf2cGA==', 'OD2208307wRQrjIhQM2JOYDmzqZqjg==',
		  'OD220829jB1dLDIGRL+fJfPtAEFHug==', 'OD220829/zZZPGm7Txa0AD7GgDsckg==', 'OD2208290X82M8dTRZqI29ebjQfLTg==',
		  'OD220829tgQmYvjjQ1Kqy3Xovc0GJA==', 'OD220829HqO9pOn8SiufsYfvAJ0kPw==', '*********/qarUqXTHyRpxNatqp+kQ==',
		  'OD220828hgBGUZHnSoODwproOF1bqQ==', 'OD220827U+i2B0q6RuOKM+aqI693Hg==', 'OD220827Ib5b190WSC+QHQ9aJCY1Iw==',
		  'OD220827/dfXNs3pSGGMUShHoX2CTg==', 'OD220826KyG0F8ySRrGEU+Ikx0qBcQ==', 'OD220826bw65j72+QluB6wd3mXzjmg==',
		  'OD220826biqMfgdSReeYey0voKaybg==', 'OD220826nVVbtncCSLSJRcMBHrnS0A==',
		  'OD220826BenXQ4vlQcS3jDlXkfycGg==')) and ((o.from_actor_id = 'AC210413eHj5Toi5Quy+wUJIodSLxA==') OR
													(o.to_actor_id = 'AC210413eHj5Toi5Quy+wUJIodSLxA=='))) AND
	   ((o.status = 'PAID' AND o.workflow NOT IN
							   ('PAID', 'ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD', 'ADD_FUNDS_COLLECT',
								'COLLECT_RECURRING_PAYMENT_NO_AUTH', 'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
		(o.status = 'SETTLED' AND o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
		(o.status = 'FULFILLED' AND o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
		(o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT'))));


WITH TXN AS (SELECT row_number() OVER ( PARTITION BY t.order_ref_id ORDER BY t.created_at DESC) AS rn,
					id,
					updated_at,
					debited_at,
					credited_at,
					order_ref_id
			 FROM transactions t
			 WHERE (((t.pi_from IN ('PI210413J6/YYZDxRym6C5ox3HheOw==', 'PI210413nvqMdO3NSySnb07VXxvRxw==',
									'PI2104134lDcuW+5RNy6e1rW2Cu3JA==', 'PI210421h4pOHHxFSVmP9U0eI6w4zg==',
									'PI2108057qda0JnISRyW0vlzJuhH4w==') AND
					  COALESCE(t.debited_at, t.credited_at, t.updated_at) <= '2022-06-30T20:47:04Z' ::TIMESTAMPTZ) OR
					 (t.pi_to IN ('PI210413J6/YYZDxRym6C5ox3HheOw==', 'PI210413nvqMdO3NSySnb07VXxvRxw==',
								  'PI2104134lDcuW+5RNy6e1rW2Cu3JA==', 'PI210421h4pOHHxFSVmP9U0eI6w4zg==',
								  'PI2108057qda0JnISRyW0vlzJuhH4w==') AND
					  COALESCE(t.debited_at, t.credited_at, t.updated_at) <= '2022-06-30T20:47:04Z' ::TIMESTAMPTZ))))
SELECT *
FROM TXN
WHERE rn = 1
ORDER BY COALESCE(debited_at, credited_at, updated_at) DESC
LIMIT 30 OFFSET 0;


select id
from orders as o
WHERE (((o.id in
		 ('OD220630uzBbUy+jSrqEJUcHTGZq+w==', 'OD220630w7FJWsm1QrCiQ+qVRUS8DQ==', 'OD220630lUnFm7atRombyipHr5m+9A==',
		  'OD220630LpJ4scdYTkuF+c+GpV8o8Q==', 'OD220629lCaJtO26TRy7aVIrhaJkcg==', 'OD220629J4GbSpYuSpuF18k7FmSWHw==',
		  'OD220629kQT+oFUdRtKZIAHpdzgNCw==', 'OD220629coiVJoVYS62/jqQQuHZDfw==', 'OD220629PzSxJDjQRs6x+Z9ffGkoDg==',
		  'OD2206283K9D8dDZRf6iY1EfkkUrSw==', 'OD220628GtWoJmpbTKy3+zhZ3lfUVg==', 'OD220628h6SsBWRWTdytxxV8YtSqlg==',
		  'OD220628JG3Qwp23QTu2+MDfL8nPpg==', 'OD220627DdWIDBzzRNiiU3bKg7bc6A==', 'OD220627dzOlZjb3SOWhQUh0SKD7Fg==',
		  'OD2206277NUg/sehQyqhRTDW2RXCNA==', 'OD2206274FJCmHLrSru0MWvtYmKkCQ==', 'OD220626oLfbTqKYQOWVHO+rLpBLKQ==',
		  'OD220626MRvLdX+MRj2/O/3ge2XyFg==', 'OD220626+xgl6DYyR5CrH5XFmP0AfA==', 'OD220626SpODllWXRX67b73W99qPdg==',
		  'OD22062620XFsreTQC2dBJBCsqijKQ==', 'OD220626WWTHVd8yTNaMuFtg01VJ3Q==', 'OD220626baJtV1cvR5e4lQXY4vI/Hw==',
		  'OD220625d4osLtFYQ4SxvHSSqLHz8w==', 'OD2206256Y5p9KB+RsWuR4BxyHLGAQ==', 'OD220625SrzWrqYiRmiAn5PP/cYpaA==',
		  'OD220625CvO5AVi+Qe6iakoAWfY2Qw==', 'OD2206259ylx6LJKS5y93DMlt2D1vQ==',
		  'OD220624Xa/hj8ENREy/+xzoWlptKQ==')) and ((o.from_actor_id = 'AC210413eHj5Toi5Quy+wUJIodSLxA==') OR
													(o.to_actor_id = 'AC210413eHj5Toi5Quy+wUJIodSLxA=='))) AND
	   ((o.status = 'PAID' AND o.workflow NOT IN
							   ('PAID', 'ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD', 'ADD_FUNDS_COLLECT',
								'COLLECT_RECURRING_PAYMENT_NO_AUTH', 'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
		(o.status = 'SETTLED' AND o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
		(o.status = 'FULFILLED' AND o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
		(o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT'))));
