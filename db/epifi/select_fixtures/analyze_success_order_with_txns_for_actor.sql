explain analyze (distsql ) WITH ORDER_WITH_TXN AS (SELECT row_number() OVER ( PARTITION BY otm.order_id ORDER BY t.created_at DESC) AS rn,
                               o.id,
                               o.created_at,
                               t.credited_at,
                               t.debited_at,
                               t.created_at as txn_created_at,
                               t.id                                                                      AS txn_id,
                               t.updated_at                                                              AS txn_updated_at
                        FROM orders_transactions_map otm
                                 INNER JOIN transactions t ON otm.transaction_id = t.id
                                 INNER JOIN orders o ON o.id = otm.order_id
                        WHERE ((t.pi_from IN ('PI220801pbURQXzzRACLJPGyfkC/Eg==', 'PI220702Vvbj5yHtRHS6q3LrVe9MmQ==',
                                              'PI220615RLxmvvVzSS2tF03lSdHztg==', 'PI220615+CUwHRfVSam2KwYRMVH87Q==',
                                              'PI220428twtvTrH7RAubvtBilcxs6w==', 'PI220428m2dA4MlrTgOh/NAapPsFtA==',
                                              'PI2204118gorg13/Q66tlJNyDQlMqg==', 'PI220112b1YIp58tT8mrGaX/XP7L4A==') OR
                                (o.to_actor_id = 'AC220107cgeCfdkTTA+55KHkFqZz/w==') AND
                                COALESCE(t.debited_at, t.credited_at, t.updated_at) >=
                                '2022-08-24 00:00:00' ::TIMESTAMPTZ) OR
                               (t.pi_to IN ('PI220801pbURQXzzRACLJPGyfkC/Eg==', 'PI220702Vvbj5yHtRHS6q3LrVe9MmQ==',
                                            'PI220615RLxmvvVzSS2tF03lSdHztg==', 'PI220615+CUwHRfVSam2KwYRMVH87Q==',
                                            'PI220428twtvTrH7RAubvtBilcxs6w==', 'PI220428m2dA4MlrTgOh/NAapPsFtA==',
                                            'PI2204118gorg13/Q66tlJNyDQlMqg==', 'PI220112b1YIp58tT8mrGaX/XP7L4A==') AND
                                COALESCE(t.debited_at, t.credited_at, t.updated_at) >=
                                '2022-08-24 00:00:00' ::TIMESTAMPTZ))
                          AND ((o.from_actor_id = 'AC220107cgeCfdkTTA+55KHkFqZz/w==') OR
                               (o.to_actor_id = 'AC220107cgeCfdkTTA+55KHkFqZz/w=='))
                          AND ((o.status = 'PAID' AND o.workflow NOT IN
                                                      ('ADD_FUNDS', 'REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD',
                                                       'ADD_FUNDS_COLLECT', 'COLLECT_RECURRING_PAYMENT_NO_AUTH',
                                                       'COLLECT_RECURRING_PAYMENT_WITH_AUTH')) OR
                               (o.status = 'SETTLED' AND
                                o.workflow IN ('ADD_FUNDS', 'ADD_FUNDS_COLLECT', 'P2P_INVESTMENT')) OR
                               (o.status = 'FULFILLED' AND
                                o.workflow IN ('REWARDS_CREATE_SD', 'REWARDS_ADD_FUNDS_SD')) OR
                               (o.status = 'IN_SETTLEMENT' AND o.workflow IN ('P2P_INVESTMENT'))))
SELECT *
FROM ORDER_WITH_TXN
WHERE rn = 1
ORDER BY COALESCE(debited_at, credited_at, txn_updated_at) desc
	LIMIT 30 OFFSET 0 ;
