ALTER TABLE user_group_mappings ALTER COLUMN user_email SET NOT NULL;
ALTER TABLE user_group_mappings ALTER COLUMN identifier_value SET DEFAULT 'IDENTIFIER_VALUE';
ALTER TABLE user_group_mappings ALTER COLUMN identifier_type SET DEFAULT 'IDENTIFIER_TYPE_EMAIL';

-- change primary key
BEGIN;
ALTER TABLE user_group_mappings DROP CONSTRAINT "user_group_mappings_pkey";
ALTER TABLE user_group_mappings ADD CONSTRAINT "primary" PRIMARY KEY (user_email, user_group, deleted_at_unix);
COMMIT;
