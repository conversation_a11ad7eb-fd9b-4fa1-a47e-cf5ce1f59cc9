-- Update panName in DATA_TYPE_PAN_NAME for the specified user
UPDATE public.users
SET data_verification_details = jsonb_set(
	data_verification_details,
	'{dataVerificationDetails}',
	(
		SELECT jsonb_agg(
				   CASE
					   WHEN elem->>'dataType' = 'DATA_TYPE_PAN_NAME' THEN
						   jsonb_set(
							   jsonb_set(
								   jsonb_set(elem, '{panName,firstName}', '"PUSHPENDRA"'),
								   '{panName,middleName}', '""'
							   ),
							   '{panName,lastName}', '"NINAMA"'
						   )
					   ELSE elem
					   END
			   )
		FROM jsonb_array_elements(data_verification_details->'dataVerificationDetails') AS elem
	)
								)
WHERE id = '4c558d98-efe1-43a5-b3e4-eb91918979bf';
