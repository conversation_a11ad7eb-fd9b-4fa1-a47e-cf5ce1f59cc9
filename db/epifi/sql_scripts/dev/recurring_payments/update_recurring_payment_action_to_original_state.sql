
-- Revert state change for the first record
UPDATE recurring_payments_actions
SET state = 'ACTION_CREATED', updated_at = NOW()
WHERE id = 'c60a6258-97f9-4300-923b-9906f5249622'
  AND client_request_id = 'f73241cf-01ae-4c5b-ba86-51fa0f5da617'
  AND state = 'ACTION_SUCCESS'
  AND action = 'EXECUTE';

-- Revert soft delete for the second record
UPDATE recurring_payments_actions
SET deleted_at = NULL, updated_at = NOW()
WHERE id = '3701ef60-177a-4e5c-b48a-19021efc110e'
  AND client_request_id = 'f73241cf-01ae-4c5b-ba86-51fa0f5da617'
  AND state = 'ACTION_CREATED'
  AND action = 'EXECUTE';
