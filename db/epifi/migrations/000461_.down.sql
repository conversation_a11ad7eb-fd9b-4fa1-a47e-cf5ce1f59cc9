DROP TABLE IF EXISTS public.bank_customers;
CREATE TABLE IF NOT EXISTS public.bank_customers (
									   id UUID NOT NULL DEFAULT gen_random_uuid(),
									   bank_customer_id STRING NULL,
									   actor_id STRING NOT NULL,
									   user_id STRING NOT NULL,
									   name <PERSON><PERSON><PERSON><PERSON> NULL,
									   vendor STRING NOT NULL,
									   status STRING NULL,
									   creation_started_at TIMESTAMPTZ NULL,
									   vendor_creation_succeeded_at TIMESTAMPTZ NULL,
									   fi_creation_succeeded_at TIMESTAMPTZ NULL,
									   kyc_level_update_flow STRING NULL,
									   vendor_metadata JSONB NULL,
									   dedupe_info JSONB NULL,
									   failure_reason JSONB NULL,
									   account_closure_info JSONB NULL,
									   created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
									   updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
									   deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
									   computed_cif_request_id STRING NULL AS ((vendor_metadata->'federalMetadata':::STRING)->>'cifRequestId':::STRING) STORED,
									   computed_kyc_level STRING NULL AS (dedupe_info->>'kycLevel':::STRING) STORED,
									   CONSTRAINT "primary" PRIMARY KEY (actor_id ASC, id ASC),
									   UNIQUE INDEX bank_customers_user_id_deleted_at_unix_idx (user_id ASC, vendor ASC, deleted_at_unix ASC),
									   UNIQUE INDEX bank_customers_actor_id_deleted_at_idx (actor_id ASC, vendor ASC, deleted_at_unix ASC),
									   UNIQUE INDEX bank_customers_bank_customer_id_vendor_idx (bank_customer_id ASC, vendor ASC),
									   INDEX bank_customers_updated_at_idx (updated_at ASC),
									   UNIQUE INDEX bank_customers_id_unique_idx (id ASC),
									   FAMILY seldom_updated (vendor_metadata),
									   FAMILY "primary" (id, bank_customer_id, actor_id, user_id, name, vendor, status, creation_started_at, vendor_creation_succeeded_at, fi_creation_succeeded_at, kyc_level_update_flow, dedupe_info, failure_reason, account_closure_info, created_at, updated_at, deleted_at_unix, computed_cif_request_id, computed_kyc_level)
);
COMMENT ON COLUMN public.bank_customers.status IS '{"proto_type":"user.bank_customer.Status", "comment": "indicates the overall status of customer creation"}';
COMMENT ON COLUMN public.bank_customers.kyc_level_update_flow IS '{"proto_type":"user.bank_customer.KycLevelUpdateFlow", "comment": "denotes the flow via which kyc level was updated"}';
COMMENT ON COLUMN public.bank_customers.vendor_metadata IS '{"proto_type":"user.bank_customer.VendorMetadata", "comment": "contains vendor specific data"}';
COMMENT ON COLUMN public.bank_customers.account_closure_info IS '{"proto_type":"user.bank_customer.AccountClosureInfo", "comment": "contains account closure info}';
