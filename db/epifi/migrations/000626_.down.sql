CREATE TABLE public.token_stores (
									 id STRING NOT NULL,
									 created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
									 updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
									 deleted_at TIMESTAMPTZ NULL,
									 status STRING NOT NULL,
									 device JSONB NOT NULL,
									 token STRING NULL,
									 token_type STRING NULL,
									 phone_number JSONB NOT NULL,
									 email STRING NULL,
									 last_activity TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
									 device_registration_status STRING NULL,
									 auth_factor_update_id UUID NULL,
									 actor_id STRING NOT NULL,
									 computed_phone_number STRING NULL AS (CASE WHEN ((phone_number->>'country_code':::STRING) IS NOT NULL) AND ((phone_number->>'national_number':::STRING) IS NOT NULL) THEN concat(phone_number->>'country_code':::STRING, phone_number->>'national_number':::STRING) ELSE NULL END) STORED,
									 device_integrity_id STRING NULL,
									 device_integrity_result STRING NULL,
									 CONSTRAINT "primary" PRIMARY KEY (id ASC),
									 INDEX actor_id_deleted_at_idx (actor_id DESC, deleted_at ASC),
									 INDEX token_stores_updated_at_idx (updated_at ASC),
									 INDEX token_stores_computed_phone_number_token_type_created_at_idx (computed_phone_number ASC, token_type ASC, created_at DESC),
									 INDEX token_stores_actor_id_token_type_created_at_idx (actor_id ASC, token_type ASC, created_at DESC)
);
