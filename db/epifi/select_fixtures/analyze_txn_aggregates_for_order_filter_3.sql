explain
analyze (distsql )
SELECT sum(t.computed_amount) as Sum<PERSON>mount, count(*) as Count FROM transactions t  INNER JOIN orders o ON t.order_ref_id = o.id  WHERE  (t.pi_from in ('PI210217Gw8xjTDHSxm0TEGbR06kYg==','PI210217Z9JpdAqHSPmdU7hyLWwu4Q==','PI210217uSJAA9//SNerPzlseGjrWQ==') OR t.pi_to in ('PI210217Gw8xjTDHSxm0TEGbR06kYg==','PI210217Z9JpdAqHSPmdU7hyLWwu4Q==','PI210217uSJAA9//SNerPzlseGjrWQ=='))  AND (COALESCE(t.debited_at, t.credited_at, t.created_at) >= '2022-05-22 20:08:38.894' ::TIMESTAMPTZ AND COALESCE(t.debited_at, t.credited_at, t.created_at) <= '2022-12-01 12:58:38.894' ::TIMESTAMPTZ)  AND (tags @> '{"Tags":["REWARD"]}'::jsonb);
