-- dropping foriegn key constraint on actor id on all the tables present in epifi db
-- this has to be done since we are migrating actors table from crdb to pgdb, hence the foriegn key constraint will fail for the tables present in epifi db
-- also it is not a good practice to have forieng key amoung the tables of different services, hence this will not added in pgdb even if all the tables are migrated
ALTER TABLE IF EXISTS   connected_accounts
DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   account_pis
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   actor_pi_resolutions
	DROP CONSTRAINT IF EXISTS fk_actor_from_ref_actors;

ALTER TABLE IF EXISTS   actor_pi_resolutions
	DROP CONSTRAINT IF EXISTS fk_actor_to_ref_actors;

ALTER TABLE IF EXISTS   cards
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   face_match_attempts
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   liveness_attempts
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   timelines
	DROP CONSTRAINT IF EXISTS fk_primary_actor_id_ref_actors;

ALTER TABLE IF EXISTS   timelines
	DROP CONSTRAINT IF EXISTS fk_secondary_actor_id_ref_actors;

ALTER TABLE IF EXISTS   orders
	DROP CONSTRAINT IF EXISTS fk_actor_from_ref_actors;

ALTER TABLE IF EXISTS   orders
	DROP CONSTRAINT IF EXISTS fk_actor_to_ref_actors;

ALTER TABLE IF EXISTS   referrals
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   referral_relations
	DROP CONSTRAINT IF EXISTS fk_referrer_actor_id_ref_actors;

ALTER TABLE IF EXISTS   referral_relations
	DROP CONSTRAINT IF EXISTS fk_referee_actor_id_ref_actors;

ALTER TABLE IF EXISTS   blocked_actors_map
	DROP CONSTRAINT IF EXISTS fk_blocked_actors_map_actor_id;

ALTER TABLE IF EXISTS   blocked_actors_map
	DROP CONSTRAINT IF EXISTS fk_blocked_actors_map_blocked_actor_id;

ALTER TABLE IF EXISTS   vendor_otps
	DROP CONSTRAINT IF EXISTS fk_vendor_otps_actor_id;

ALTER TABLE IF EXISTS   deposit_accounts
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   deposit_requests
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   contacts
	DROP CONSTRAINT IF EXISTS fk_contacts_actor_id;

ALTER TABLE IF EXISTS   disputed_transactions
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   card_sku_overrides
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   device_reg_attempts
	DROP CONSTRAINT IF EXISTS foreign_key_actor_id_ref_actors;

ALTER TABLE IF EXISTS   finite_codes
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   finite_code_claims
	DROP CONSTRAINT IF EXISTS fk_referrer_actor_id_ref_actors;

ALTER TABLE IF EXISTS   finite_code_claims
	DROP CONSTRAINT IF EXISTS fk_referee_actor_id_ref_actors;

ALTER TABLE IF EXISTS   inappreferral_unlocks
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   deposit_account_bonus_payouts
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;

ALTER TABLE IF EXISTS   card_action_attempts
	DROP CONSTRAINT IF EXISTS fk_card_action_attempts_actor_id;

ALTER TABLE IF EXISTS   goals
	DROP CONSTRAINT IF EXISTS fk_actor_id_ref_actors;
