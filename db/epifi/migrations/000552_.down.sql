ALTER TABLE risk_evaluator_entities ALTER PRIMARY KEY USING COLUMNS (entity_type, entity_id);
ALTER TABLE risk_evaluator_entities DROP COLUMN id;
ALTER TABLE risk_evaluator_entities ADD COLUMN id string not null;
ALTER TABLE risk_evaluator_entities ALTER PRIMARY KEY USING COLUMNS (entity_type asc, entity_id asc, id asc);
DROP INDEX IF EXISTS risk_evaluator_entities_entity_vendor_idx;
-- Need to drop following index as it gets created as a result of primary key addition in the first step
DROP INDEX IF EXISTS risk_evaluator_entities_entity_type_entity_id_key;
