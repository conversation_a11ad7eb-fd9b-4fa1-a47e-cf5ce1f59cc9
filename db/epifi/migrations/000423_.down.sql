drop table if exists workflow_histories;
create table if not exists workflow_histories (
	wf_req_id        	STRING NOT NULL,
	stage            	STRING NOT NULL,
	status           	STRING NOT NULL,
	created_at       	TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at       	TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at       	TIMESTAMPTZ NULL,
	completed_at 	 	TIMESTAMPTZ NULL,
	attempts 		    INTEGER DEFAULT 1 NOT NULL,
	failure_description STRING NULL,
	CONSTRAINT workflow_history_wf_req_id_fkey FOREIGN KEY (wf_req_id) REFERENCES workflow_requests(id)
);

COMMENT ON COLUMN workflow_histories.wf_req_id IS 'foreign key from workflows req table';
COMMENT ON COLUMN workflow_histories.stage IS 'Current stage of the workflow';
COMMENT ON COLUMN workflow_histories.status IS 'Latest status of the stage';
COMMENT ON COLUMN workflow_histories.attempts IS 'Number of attempts made to process the order stage. This can be used by the orchestration engine to handle poison pill request during batch processing, etc.';
COMMENT ON COLUMN workflow_histories.failure_description IS 'Optional: Failure description as returned from the activity. The same can be used to display message on the UI if needed. ';
