-- explain analyze the older order_with_txn query for benchmarking purpose
explain analyze (distsql) WITH ORDER_WITH_TXN AS ( SELECT row_number() OVER ( PARTITION BY o.id ORDER BY t.created_at DESC) AS rn, o.id,o.created_at ,  t.created_at AS txn_created_at,  t.status AS txn_status, t.id AS txn_id FROM orders o LEFT JOIN transactions t ON o.id = t.order_ref_id WHERE IF ( t.id IS NOT NULL, ( ( t.pi_from IN ('PI210904E3HH0mmiSNSkIwTDAOYHJQ==','PI210904TWtRAQvtTEixiuXAM5FVzg==','PI210904vX2sQ1iZQ/G3MWNmzlBNvQ==','PI220630CF0OQqFvReaNhz6n4nj8BQ==')) OR ( t.pi_to IN ('PI210904E3HH0mmiSNSkIwTDAOYHJQ==','PI210904TWtRAQvtTEixiuXAM5FVzg==','PI210904vX2sQ1iZQ/G3MWNmzlBNvQ==','PI220630CF0OQqFvReaNhz6n4nj8BQ==')) ) AND t.created_at <= '2022-08-12 12:58:41.372' ::TIMESTAMPTZ , o.workflow = 'P2P_COLLECT_SHORT_CIRCUIT' )AND o.created_at <= '2022-08-12 12:58:41.372' ::TIMESTAMPTZ AND o.status NOT IN ('CREATED','REJECTED') AND (( o.from_actor_id = 'AC210902UDY+LpGES6mkLL41SrfMuQ==' AND o.to_actor_id = 'AC2103240dzOiSO9Tmm/7rh25LPEJw==' AND IF( o.workflow IN ('P2P_COLLECT_SHORT_CIRCUIT') , o.status NOT IN ('COLLECT_FAILED','COLLECT_IN_PROGRESS','CREATED'), true) )OR ( o.from_actor_id = 'AC2103240dzOiSO9Tmm/7rh25LPEJw==' AND o.to_actor_id = 'AC210902UDY+LpGES6mkLL41SrfMuQ==' AND IF( o.workflow IN ('P2P_COLLECT_SHORT_CIRCUIT','P2P_COLLECT','ADD_FUNDS','ADD_FUNDS_COLLECT','P2P_COLLECT','COLLECT_RECURRING_PAYMENT_WITH_AUTH','COLLECT_RECURRING_PAYMENT_NO_AUTH') , true, o.status in ('PAID','FULFILLED') ) ) ) ) SELECT * FROM ORDER_WITH_TXN WHERE rn = 1 ORDER BY IFNULL(txn_created_at, created_at) DESC LIMIT 3 OFFSET 0;
-- adding the index hint
explain analyze (distsql) WITH ORDER_WITH_TXN AS ( SELECT row_number() OVER ( PARTITION BY o.id ORDER BY t.created_at DESC) AS rn, o.id,o.created_at ,  t.created_at AS txn_created_at,  t.status AS txn_status, t.id AS txn_id FROM orders@orders_outbound_actor_lookup_idx o LEFT JOIN transactions t ON o.id = t.order_ref_id WHERE IF ( t.id IS NOT NULL, ( ( t.pi_from IN ('PI210904E3HH0mmiSNSkIwTDAOYHJQ==','PI210904TWtRAQvtTEixiuXAM5FVzg==','PI210904vX2sQ1iZQ/G3MWNmzlBNvQ==','PI220630CF0OQqFvReaNhz6n4nj8BQ==')) OR ( t.pi_to IN ('PI210904E3HH0mmiSNSkIwTDAOYHJQ==','PI210904TWtRAQvtTEixiuXAM5FVzg==','PI210904vX2sQ1iZQ/G3MWNmzlBNvQ==','PI220630CF0OQqFvReaNhz6n4nj8BQ==')) ) AND t.created_at <= '2022-08-12 12:58:41.372' ::TIMESTAMPTZ , o.workflow = 'P2P_COLLECT_SHORT_CIRCUIT' )AND o.created_at <= '2022-08-12 12:58:41.372' ::TIMESTAMPTZ AND o.status NOT IN ('CREATED','REJECTED') AND (( o.from_actor_id = 'AC210902UDY+LpGES6mkLL41SrfMuQ==' AND o.to_actor_id = 'AC2103240dzOiSO9Tmm/7rh25LPEJw==' AND IF( o.workflow IN ('P2P_COLLECT_SHORT_CIRCUIT') , o.status NOT IN ('COLLECT_FAILED','COLLECT_IN_PROGRESS','CREATED'), true) )OR ( o.from_actor_id = 'AC2103240dzOiSO9Tmm/7rh25LPEJw==' AND o.to_actor_id = 'AC210902UDY+LpGES6mkLL41SrfMuQ==' AND IF( o.workflow IN ('P2P_COLLECT_SHORT_CIRCUIT','P2P_COLLECT','ADD_FUNDS','ADD_FUNDS_COLLECT','P2P_COLLECT','COLLECT_RECURRING_PAYMENT_WITH_AUTH','COLLECT_RECURRING_PAYMENT_NO_AUTH') , true, o.status in ('PAID','FULFILLED') ) ) ) ) SELECT * FROM ORDER_WITH_TXN WHERE rn = 1 ORDER BY IFNULL(txn_created_at, created_at) DESC LIMIT 3 OFFSET 0;
