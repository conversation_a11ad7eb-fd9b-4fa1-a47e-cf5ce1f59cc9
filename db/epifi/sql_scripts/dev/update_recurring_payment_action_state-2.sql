-- https://monorail.pointz.in/p/fi-app/issues/detail?id=78609
-- Fixing UPI Mandates issues in CUG
-- Recurring payment PAUSE action failed on vendor side. So in order to retry the same action, prev
-- action needs to be in terminal state. So creating this fixture in order to easily update the action
-- state for the latest recurring payment action for a given recurring payment id.

UPDATE RECURRING_PAYMENTS_ACTIONS SET STATE = 'ACTION_FAILURE', updated_at = NOW() WHERE CLIENT_REQUEST_ID = (SELECT CLIENT_REQUEST_ID FROM RECURRING_PAYMENTS_ACTIONS WHERE RECURRING_PAYMENT_ID = 'RP/uTyozlnTCW8ACFSaLeaMg240408==' ORDER BY RECURRING_PAYMENTS_ACTIONS.CREATED_AT DESC LIMIT 1);
