--CX issue Ticket: 3983889, Due to different transparticular, below Deposit is not marked as CLOSED. Deposit closures were credited to user
--Deposit Details
--deposit pi --> PI230228bhVP7OC7RMyAc/ni2HoHaw== for deposit number **************
--"ActorId": AC220415mV7roaHiTIyYSL6H02GxWQ==




UPDATE deposit_accounts
set state='CLOSED' , updated_at = NOW()
where state = 'CREATED'
  and id = 'DP230228fmAclWr4SvyOYqWCE/Aahw=='
and actor_id ='AC220415mV7roaHiTIyYSL6H02GxWQ==' ;

