UPDATE risk_bank_actions
SET request_reason = '{"reason": "REQUEST_REASON_OTHER", "remarks": "Onboarding and transaction parameters met  - Account Unfrozen"}', comms_template = '{COMMS_TEMPLATE_EMAIL_ACC_UNF}'
WHERE actor_id IN ('AC220525Ik5EV75CSYKaGZ/8KzsfmA==','AC211124fOTq7p8NQlqVBPOTF8DoNw==','AC220503r+za1hl+RIiaZ6RSxDQ0nw==','AC211217X+UvL8zCQj2tr4SnFqQYjw==','ACv2YH/h73SIav1HGlwF8lqQ230802==','AC220103xVS0ZQcEQv+SyUIL5MYD8Q==','AC230226zKjUvsz9TfKGs2T1cnajOg==','AC22050148QFowEBTriTvljH8HZW8Q==','AC220313IBv3dVjkRsGBVbNZaWl+nQ==','AC2203045B0ILdVxR52Go078UeciiA==','AC211012UXM61jNqQmOCUBcDmNKHmw==','ACLbzPNY/1Q5qPY/RBkl+vHQ230719==','AC221105h418w3hqTLaK6G/65HCBaQ==','AC220825vRniaIsyRy+8Neu4h4/Dmw==','AC220407gc7Vs0UXSqip5SbJslQLDg==','AC211102iPF8y8CdTgKVkPZVfMgZaw==','AC220715SZ+UEDRsQhGDroR6vrH4VQ==','AC2105294lmlh8sLT5yvmRg3AbT3Ag==','AC211126LNzCn8yKSPOgGq6mOG/Aaw==','AC220203T1d+ZE8GS1mcHJgMhmGIFw==','AC220314qKRuLM36S/KRdAbXZ9R2eA==','AC211231YCu+evw6RKWc7QzIbzQbqg==','AC220429R0oZfPChRbyyoBc110ROnQ==','AC220127MghnKjTOQS2J1t79uUYBtw==','AC230310F4kFNwDwS1G1cIi0wGMztA==','AC220721GHtOPSX1RFC+5dx0FqnI7Q==','AC221126Z3Ye+eY0TEe7qIe99OZ78w==','AC220907GofqqSjKTTOVdpH72RQD6w==','AC220310OWYFX0TySVqQw2VNGpPtOQ==','AC221228poeF6Kh1TVWE6Ek4DGt/Ug==','ACmnn7ZWuTTuqClYuhs1yM8g230519==','AC210731+g6Ljn56ShqXLXJgPlNcWg==','AC220109y19/pbe2TYOpTSgqoPC8OQ==','AC211009o07IPmnnRdCJ7zwI/7+Tng==','AC211201ZMMwA/HNS/2j5Im46WJhpA==','AC220122l7yDsGtFQROXKfFnf0lJIw==','AC220119BAqqaDjuTzOn/I0kbd/6GQ==','AC230111M0jgg55PTUuA0tUk7Myc3w==','AC220311zUDMWZ1TRrKc1PewLAws9Q==','ACvlC+B+YvQHKcp3BpdRwTxg231118==','AC211121T5F3Fv3ySJOUUe2+rp+zXA==','AC220920I1sjdjsbRxy9kXFvzIx7Aw==','AC220201jWWHAEqxTAWNwmp9omVWDg==','AC220404yDqFIrGbSzqWMCk4ttxn3g==','AC220821igaW3g/YSqG0pqmZpMvq5A==') AND action = 'ACTION_UNFREEZE' AND created_at between '2024-07-23 00:00:00.000000 +05:30' and '2024-07-23 23:59:59.000000 +05:30';