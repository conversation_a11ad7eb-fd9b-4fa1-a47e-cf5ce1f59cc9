-- The actor is seeing  wrong computed balance on app. Hence, we need to fetch any pending transaction
-- to  see why the balance is wrong
SELECT transactions.id, transactions.payment_protocol, transactions.status
from transactions
WHERE (pi_from IN (select pi_id from account_pis where actor_id = 'AC2105171/9NnuceTbuxFacN/lKa1Q==') OR
	   pi_to IN (select pi_id from account_pis where actor_id = 'AC2105171/9NnuceTbuxFacN/lKa1Q=='))
  AND status IN ('MANUAL_INTERVENTION', 'UNKNOWN', 'IN_PROGRESS');
