-- fetch legacy transactions where utr is wrong for given protocol
SELECT id, utr, partner_ref_id, partner_ref_id_debit, partner_ref_id_credit, payment_protocol
FROM transactions
WHERE IFNULL(utr != partner_ref_id, true)
  AND IFNULL(utr != partner_ref_id_debit, true)
  AND IFNULL(utr != partner_ref_id_credit, true)
  AND (payment_protocol = 'PAYMENT_PROTOCOL_UNSPECIFIED' OR payment_protocol IS NULL);
