-- select all the pending transactions without serialID for actor `AC201212WWu2b8lyS1O/O+quF9JR8g==`
SELECT id
FROM transactions
WHERE (pi_from IN
       ('PI201224IrG+FKVnSo+OUx3pBCKe7w==', 'PI201224bHNDlVjzQ6yvLAOTVX4tBA==', 'PI201224dOE5nSWFTS63yoAC5wRTTg==',
        'PI201230HG7kr/BpTO+O5Jq3OiGR0Q==', 'PI210112s04/4sj1RNu/Mma8QVlggg==', 'PI210114E7qixsluSu+ExdrsUvw80Q==',
        'PI210114rK111y8uQWSZsG84+xQFEA==', 'PI210114xLvQGCA0SFijcdbPCqlX9w==', 'PI210302iBKLgxS6TLiTMhGR3SUE6g==',
        'PI210403LGVEkvsPQAe28RoKovwmfw==', 'PI210411+f1/b1YvT+CiFlRU1bfzkQ==', 'PI210412b7XI+hQJQp6Id1tW/N2pPw==',
        'PI210413EbmeLHyAT0awnCTCD02enA==', 'PI210414lwMUV4/lRTaqedkF/q1pfw==', 'PI210424NPHXzkJ9QPO6IsFoX2nIxg==',
        'PI210519+xIQFyDPRxuwLnm2hGLifA==', 'PI210519YnmhbFzfRoOx0X9gjO9TTA==', 'PI210611UP9W/BWGTGaVAg0K3FAYFA==',
        'PI210630//hnfZ/rQEm25g9I0EBlBw==') OR
       pi_to IN
       ('PI201224IrG+FKVnSo+OUx3pBCKe7w==', 'PI201224bHNDlVjzQ6yvLAOTVX4tBA==', 'PI201224dOE5nSWFTS63yoAC5wRTTg==',
        'PI201230HG7kr/BpTO+O5Jq3OiGR0Q==', 'PI210112s04/4sj1RNu/Mma8QVlggg==', 'PI210114E7qixsluSu+ExdrsUvw80Q==',
        'PI210114rK111y8uQWSZsG84+xQFEA==', 'PI210114xLvQGCA0SFijcdbPCqlX9w==', 'PI210302iBKLgxS6TLiTMhGR3SUE6g==',
        'PI210403LGVEkvsPQAe28RoKovwmfw==', 'PI210411+f1/b1YvT+CiFlRU1bfzkQ==', 'PI210412b7XI+hQJQp6Id1tW/N2pPw==',
        'PI210413EbmeLHyAT0awnCTCD02enA==', 'PI210414lwMUV4/lRTaqedkF/q1pfw==', 'PI210424NPHXzkJ9QPO6IsFoX2nIxg==',
        'PI210519+xIQFyDPRxuwLnm2hGLifA==', 'PI210519YnmhbFzfRoOx0X9gjO9TTA==', 'PI210611UP9W/BWGTGaVAg0K3FAYFA==',
        'PI210630//hnfZ/rQEm25g9I0EBlBw=='))
  AND (array_length(regexp_split_to_array(utr, ':'), 1) = 2 -- filter cases <date>:cbsID
    OR (array_length(regexp_split_to_array(utr, ':'), 1) = 3 AND
        regexp_split_to_array(utr, ':')[1] = 'EXTENDED')) -- filter cases EXTENDED:<date>:cbsID
  AND partner_bank = 'FEDERAL_BANK';
