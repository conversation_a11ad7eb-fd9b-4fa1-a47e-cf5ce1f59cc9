-- Query to understand the performance implications of the transaction aggregates join query with index hints
-- PIs used is of internal actor @nitesh
EXPLAIN
ANALYZE(distsql)
SELECT sum(t.computed_amount) AS sumamount, count(*) AS count
FROM transactions@transactions_pi_from_created_at_status_idx AS t INNER JOIN orders AS o
ON t.order_ref_id = o.id
WHERE (
	(
	(
	(t.pi_from IN ('PI210808r+F4p31SSR29XOCGNsjcjg=='
	, 'PI210807pWnaIlnNTlOhC2Wsc1r2xA=='
	, 'PI210806rzLYGKRbSB+gxnYmFxR1qQ=='
	, 'PI210806XMNrC6nSQFS9MqiuuGz5xw=='
	, 'PI210806MUORgEWBSKuxXpwcLCgGsA==')
  AND t.status IN ('SUCCESS'))
  AND (
	COALESCE (t.debited_at
	, t.credited_at
	, t.created_at) >= '2023-01-01 07:37:12.498'::TIMESTAMPTZ
  AND COALESCE (t.debited_at
	, t.credited_at
	, t.created_at) <= '2023-01-12 23:27:12.498'::TIMESTAMPTZ
	)
	)
  AND t.created_at >= '2023-01-01 07:37:12.498'::TIMESTAMPTZ
	)
  AND t.created_at <= '2023-01-12 23:27:12.498'::TIMESTAMPTZ
	)
  AND tags @
	> '{"Tags":["MERCHANT"]}'::JSONB;
