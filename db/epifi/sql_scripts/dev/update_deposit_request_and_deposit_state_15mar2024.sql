-- User preclosed deposit account and we got success response from federal but the request failed at their end
--reverting the request success state to failed then deposit state to CREATED


update
	deposit_requests
set state = 'REQUEST_FAILED',
	updated_at = now()
where id = 'DRPh9dWUcHR+6Ry13R0NVykw240301==' and deposit_account_id = 'DPIPaQw1RMQXyu492tKGmdVg240228==';

update
	deposit_accounts
set state = 'CREATED',
	updated_at = now()
where id='DPIPaQw1RMQXyu492tKGmdVg240228==' and actor_id ='AC220708ecLOnW8aRNmK8PZoZQt3Vg==';
