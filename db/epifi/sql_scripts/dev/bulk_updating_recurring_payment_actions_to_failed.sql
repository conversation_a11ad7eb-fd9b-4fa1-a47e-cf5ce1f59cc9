-- slack ref: https://epifi.slack.com/archives/C031GNXERB9/p1714395532218069
-- updating Execute type recurring payment action status to failed as the the corresponding payment order is Failed

update recurring_payments_actions
set state = 'ACTION_FAILURE', updated_at = NOW()
where id in  ('89878713-816b-4f8e-8935-4410661213b5',
			  '6e8e69e2-82e4-4db5-9989-72f922eeebdc',
			  '81f749f9-c313-4b4f-ae43-a21c351f21e0',
			  'db1cdb84-b5e8-4bf4-9b70-4dae1ffaf63e',
			  '6ca8c842-7e82-42b3-8736-e5b392f475be',
			  '576f483b-a90d-4966-a07d-b2ae05d671d0',
			  '17273b45-27fc-4e29-85fb-5f9a79fa8adc',
			  '12f8f79b-3005-4e6e-8e2d-076849a5f835',
			  '5436dfa9-ae7d-41a7-9867-8424b594bb28',
			  'd4d87cf5-bbfc-4cdd-8122-ef6d6941a9b6',
			  'dfec5e87-0b83-41c8-bb51-93193c3dbb3c',
			  '4d2719cb-3ee1-4d56-80f2-1546f4bf6311',
			  'e1375119-450f-424d-a890-6fcab2885c3f',
			  'f9cab47b-ed65-4934-828d-5cb8071358ec',
			  '8a7b5035-b524-4a98-974b-f501fec2ef15',
			  'db5b1b11-87e0-4add-8b95-297b7ffb9d63',
			  '9f9bf868-eb3c-4702-9cca-935366dcab7e',
			  'ef5065e3-0577-4bfa-a78c-6c2f4bf44492',
			  'ae4bd14d-c687-414c-b99f-8705a43cb29c',
			  '1dfd7938-7930-4472-9c1c-0bedaeee3485',
			  '2fee0b9e-1ca7-4e94-bc0a-620017d941a4',
			  'f1aa924b-e5e4-4072-8bd3-5a101c6d7530',
			  'cdb8f499-410e-4e93-9a80-30625d95e25c',
			  '778d91f2-5dcc-461d-a352-e5b48cccec36',
			  'b028084d-d233-4539-ab91-922f507eb1f5',
			  '57519178-f2a3-4e10-8f37-85ce9d8ea918',
			  'bbe15793-b668-4a92-bd9c-5b545d5891e6',
			  '057eb6df-de22-4f8c-a89e-ef4dec3bea32',
			  '7eb29c54-4422-4f42-b49b-6890e497ebcb',
			  '606f8920-6de1-41be-be5e-0fb3c0e83f2e',
			  'e3ff22ea-2045-4712-bb0f-60f26b99c6a8',
			  '376e7843-ca08-4bb6-800a-583c6d26e8da',
			  'a50f69c2-bb4f-4f92-89d2-c149ceb277ee',
			  '0bc59622-53e5-4277-b542-633532c49616',
			  '783c1e3c-2675-40a7-b8d3-ddaf5ef61af2',
			  '73cdfa87-1fd3-441c-8076-28cf3597c978',
			  'b56b7894-80f0-4858-8445-250f3873933a',
			  '69691e7d-0634-41ce-bf54-0d780ce2b336',
			  '69882e40-6e54-4e53-bdc3-0a88f11301d8',
			  '120e0d1e-6c17-4f27-94d0-58aeb43a48ea',
			  'fa292fcc-82d9-4bef-ac3d-2ed045c5ea84',
			  '71f0299f-f5ed-4627-b1b6-dfa121753111',
			  'e3452037-e4e1-4f7a-b1aa-665a13035307',
			  '8f61d925-d40d-4566-b755-5a70d6f0448a',
			  'efd543a9-ee09-4b2a-a25e-63e4a23a9e72',
			  '4ecccd66-12a2-4353-9e92-bbb56070e9b2',
			  '15aaf49e-50ef-40fa-9b78-3f0c71cd29f3',
			  'ba72e7e3-b551-45b9-8b27-ec3018bdbc1f',
			  'e4d0833c-af03-4788-a15c-29519eeae380',
			  '20c6a186-2d45-4359-9415-3b5660c467aa',
			  '1bf84012-61a2-4545-95d3-186c119c530c',
			  'f3a2ad0a-93d3-45f1-835c-b13c32a390f2',
			  '4db0411f-7eaa-4481-936c-015226c019f3',
			  '3c0875a2-cf13-4e6c-9e06-66a2c111cdc2',
			  'f14f39f1-9e5f-4ce4-b4c3-d59976f01656',
			  '55bc8b00-7103-4717-815c-232b5f223554',
			  'da20626c-bbd2-409c-81e4-36b4ae923c1c',
			  'a9888246-681d-4678-800f-04faa65dcf5b',
			  '2674a50b-ec85-44a3-8e59-e094ee0c2dc4',
			  '9e1570f6-7393-4a85-8483-826ee2a4b745',
			  '2fec6d6e-93c7-4cf4-91d3-41331b1e8203',
			  '687fd718-b79b-46f8-8995-40bd39161e01',
			  '4861b28a-7a8d-4f00-97da-b16a7bd8ceb5',
			  'c4dcc5dc-3b7b-4b74-9e46-b8ba43f4d18a',
			  'c123dcfe-d783-45e6-bde0-35e513e02f39',
			  '9be050a8-ed26-4656-bcfa-ef5994837415',
			  'ffcfe6bb-5587-4ed1-91a5-1aee768b84dd',
			  '7ac4cd76-5117-4092-afc9-7b35ffd024ff',
			  '827768f8-11ac-4e02-8db6-3c30e19d7921',
			  '16b008da-2690-49eb-9547-7199df239d43',
			  'ae724bd2-e882-4994-93a2-c663333610a5',
			  '437ad3a6-5b48-4916-93e2-51cff0175d6a',
			  '91e67ca4-add4-4761-84f3-5b54e71e9d3f',
			  'b605809a-4afc-445d-b090-a3b0d3b8900f',
			  '5ef5949f-666e-43f7-8ba3-b0295634a1de',
			  '4f8b3df2-64ff-4b74-b137-e739fd068d5d',
			  'e560d095-3565-4505-b527-c94cc01c0a23',
			  '46d0a55a-bab8-4c21-b04a-8c757e18fc3a',
			  'fcc1efc7-250d-472d-bfb2-33514c552349',
			  '8aaa7c2e-88fe-4a2d-8941-aecf8e671702',
			  'f56f54f3-cd1f-4b04-ba66-f66140dd29e7',
			  'c5324fec-9b2d-485b-acbd-d55b663cf1ef',
			  '81557b07-0476-4510-b45c-39ea4b13d3c7',
			  'ad29264d-a583-4a45-953f-d62b2fdc58d1',
			  '597b804b-6f7d-4aed-8ddd-7a71accadadb',
			  '017ab98e-505e-45dd-b5cf-1033bb0923f4',
			  'a28198ba-f965-4574-bd29-4cd118411a76',
			  '52baf908-dfa0-45df-9ff0-09540ec722b0',
			  'c7e183a6-c954-4299-a657-e41d430e1afe',
			  'cb226661-c166-47f5-90d4-3b06e5628f07',
			  '155b96ac-49f9-48bb-999b-412eef07041b',
			  '4df6dd7d-e5b1-4c83-b744-53c000d95d19',
			  '6357666b-8a3f-4816-902e-e1bf1f6d8bc4',
			  '73c03ed2-9a09-43d2-a802-909c7427f971',
			  'd820d125-dcf4-4a79-bfeb-89763b6b18d4',
			  '482e1d44-846d-4767-9645-c0b42b01fe40',
			  'e3d915a9-0144-49ca-8524-3776d9a1d1ee',
			  'ea427a87-d700-4956-928f-2551c9a1c3ba',
			  '06ab4dca-c4ef-4bca-b656-120d9d280f0d',
			  '0a47f7a7-5745-4157-bafa-a5c854a1eb03',
			  'd7fbd9f8-25fd-4409-9327-07dda6834685',
			  '7a21e10d-0b50-4c4f-a415-bd2e3310af00',
			  'd2bb82ef-5dc6-4f22-acd3-f48bb08c9a64',
			  'e38e5614-bd0d-4438-81fc-72b20af6e747',
			  '94de890a-478b-4f08-95e2-cef905018186',
			  'ad884fc5-a195-41c8-90bf-3cfe76fa3ede',
			  '9c808f1d-0d1c-41fa-a8ca-4a250ff19dfa',
			  '81bbd29e-0e81-4472-91fe-cd5da07f3d9a',
			  '1557444e-46d9-486e-939b-e1b1251e020f',
			  '7e07d2be-dbb2-4042-97f4-57ae18bf9a59',
			  '08a3f191-447c-4b1f-9007-985cac3bc2bf',
			  '0706e583-1a28-48b7-83c9-32e5203a5187',
			  'fe3683b7-4cb7-48cb-a5d5-9eb667627de2',
			  '4bfd40ae-dc77-47b6-8fec-e9ee6f424d40',
			  '69e55d3d-8328-4e08-a64a-2588afa47a79',
			  '3a6f4389-6322-4236-b98b-5daac86ad8c9',
			  'ecbb4416-8c41-4541-9fa8-8a00ab518800',
			  'a4ca861a-0555-4e10-8d8e-71ce461f3da7',
			  '62a1ca77-54bd-4b43-81bb-b9c7fe6e1e0d',
			  '873a2c4b-3f75-4e7a-b2a0-9df6b5ddecff',
			  'b11e74ad-3f19-4ec5-b7ba-5195d79b2527',
			  '03d45fad-38aa-4e9c-afc1-9c2aa7837927',
			  'c4e97bfe-4231-4f11-aef6-aad3c24cc306',
			  '31f736f7-a907-44a7-8942-bcfb6fa9b436',
			  'e5678329-5397-49fb-9653-9703820891b3',
			  'bbed8062-796a-442d-bfc0-950e6fb6bd38',
			  '5cf14ade-af24-4f5d-b82a-f3c7ead02912',
			  '6a26e659-4063-4ec4-9f4b-a35514eee9cf',
			  'bbe68dfd-bcb6-489e-a998-645f74537bb1',
			  '94355991-1c57-4093-977b-51f15c3c8323',
			  '4164d6d7-b11c-473d-83ae-fa7cc61d07eb',
			  'ab35fd70-d016-4c87-aab7-1e80b16834c3',
			  '37606784-e36b-467e-a6d3-ba6b8eeeab5f',
			  '82238728-f053-41b9-85f5-b29ce4582e45',
			  'a767571a-04e1-4f99-b3b6-4dcd3fcb1d2c',
			  'ec0451e5-7e60-4e5a-8c55-4397902b6ee0',
			  '2283e935-ec1a-4ec5-bae4-38320b56797d',
			  '677bd631-3ad9-4c58-8028-7f8d47bef03c',
			  '5439fa59-6040-4b1b-ab6c-db0e1e7c3f05',
			  'c3e5ed29-9223-4935-a785-6ffdbe04216e',
			  '0aa3934a-6009-4979-966a-7fd3131c6f22',
			  'd6aee8a8-3e22-4781-aa00-0ef6f6e5ee7e',
			  'e6152c54-5d55-44a8-9a52-095630b52a77',
			  'e3adb05c-c022-461a-a08a-aedb95ff7679',
			  'b1f75695-ee3f-4459-9aac-1755bbbb0256',
			  '5d6da954-f011-4afa-8974-e3970d4cbc8b',
			  '662e72c8-65da-45a9-ad8c-9cf486a835a9',
			  'b634bf50-493f-4d7a-9245-072b689471cd',
			  '6320b933-cb4d-41e9-b83a-06430de6426c',
			  'b0daea19-184c-4011-a385-9155f70c2311',
			  '8a7abe62-5f65-4c04-b35b-ac2f176d2b69',
			  '359ac840-33a3-4df3-b49b-96d567552058',
			  'ce896f21-110a-4de9-bdb7-ac8c8d38e856',
			  'b81209e4-5c29-48bd-826f-937c195151b6',
			  'c81206f8-3c8a-4cc4-8203-2a344bc8c65c',
			  '5468b852-cb3a-46a7-825c-8afcd5e08f1d',
			  'b4865968-244f-4229-8d0a-d7e614b16f03',
			  '7ecfcc39-857b-44c8-a5a1-22dae79811f3',
			  '08763093-2a4c-4fa3-b957-9b99116aa44a',
			  '790f6b4a-add8-4a22-9f52-050d75b6f862',
			  '5cf4e634-e6a3-40c9-9239-8a5e80d67f6d',
			  'aacd58d6-56d7-4135-9a4a-08d772c88ba5',
			  '3c23535a-dbfa-4a28-811e-12d83f324a0b',
			  'decc19fd-950a-4f01-9e1f-0f1ffea2bcc0',
			  '184bec09-f3ca-4c29-a3a0-c0f145649869',
			  '1ba0882b-7eca-40a2-b791-e4aa9bb2c113',
			  'a119e1d7-a940-4045-927e-ee2b725e6d1b',
			  'bd03928d-a2f1-4115-a0ca-62ec4d70cff2',
			  'be78c067-9579-491a-a05f-17c64d36f11e',
			  'fc5e7372-5b22-482d-9ad4-24809ccb9eed',
			  '380b9331-2bdd-4afb-84a0-057848c28d7e',
			  'ad9ad6bf-07ca-4570-89cb-6c714e238ede',
			  'e2ce99eb-21ac-4d04-aea9-721194ad4cf9',
			  '7906ae9f-704e-4f94-84fc-1a0d79b48911',
			  'd0d7fab6-bbfa-4af8-be12-dd7e0debaf48',
			  'b6f7003f-a1f9-4553-863d-5754539e088b',
			  '553c1578-93fd-40f5-8e59-e90ae3ebc988',
			  'ac2d6016-9996-4226-bc40-f4b8dd6d91ae',
			  'f639490a-4f20-4e52-b864-e4192c17c08a',
			  'a40ba416-6100-43fe-970b-18299e32ce13') and state = 'ACTION_CREATED';
