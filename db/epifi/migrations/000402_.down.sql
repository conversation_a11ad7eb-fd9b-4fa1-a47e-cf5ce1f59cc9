-- Due to breaking changes, migration 382 had to be mutated after merging in master.
-- However, as a side-effect folks who had already ran the migration in their local machine by the time fix was pushed to
-- master have wrong diff in latest.sql when making any migration changes.
-- further, since the column name was change the down migration also don't help in
-- removing the column added due to the bad migration
-- Ref: https://github.com/epiFi/gamma/pull/26027
-- Hence, skipping the down migration as we dont really want the unwanted column
