CREATE TABLE public.device_params_stores (
											 id UUID NOT NULL DEFAULT gen_random_uuid(),
											 token_id STRING NOT NULL,
											 param_type STRING NOT NULL,
											 param_value STRING NOT NULL,
											 created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
											 updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
											 deleted_at TIMESTAMPTZ NULL,
											 CONSTRAINT "primary" PRIMARY KEY (id ASC),
											 INDEX device_params_stores_updated_at_idx (updated_at ASC),
											 INDEX device_params_stores_token_id_idx (token_id ASC)
);
COMMENT ON TABLE public.device_params_stores IS 'table to store device parameters such as ip address, location etc.';
