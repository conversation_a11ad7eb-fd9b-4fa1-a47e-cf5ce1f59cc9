--order details in sheet 20-Nov https://docs.google.com/spreadsheets/d/1ZpAa71T6NyZj8QVXGKFKEju_3JXhIoRbmrdqrcjqIAg/edit?usp=sharing
--updating the deposit request state and respective oms order state to failed as all these requests are initiated during CSIS
--time and stuck.
--slack conversation for the cause of the issue https://epifi.slack.com/archives/C0101Q3TXFF/p1698912390052599

UPDATE orders SET status = 'FULFILLMENT_FAILED', updated_at = NOW()
where id in ('ODNVTnMHy+TpGU6YvGh5lgzw231115==',
			 'ODg/iYJxkVQiC4gx8bkpnRkA231117==',
			 'ODP731cCZzSlebUNMWaysszw231117==',
			 'ODf+DDS7jmQdu2nwebZsKRtg231117==',
			 'ODceB4EmJwR0SLxGw7AHrqfA231117==',
			 'ODCHbOujaaT/eAILU6ZXzo/Q231117==',
			 'OD33Gef5lwT7GlrV5ZpTMXjQ231117==',
			 'ODUDwAx682QzGtgjiT+rZjgA231117==',
			 'ODDUC+My8RRCO5T6wnKGkaVw231117==',
			 'ODJs2YlU5kSO2i0YAa8k6ing231117==',
			 'ODmA8JurdJSfexT217z8v+rw231117==',
			 'ODrr7Bc3XKTPWeXwTtezBkSg231117==',
			 'ODaBdbpduFRMya8V+2VtNPgA231117==',
			 'ODfRUg0GQDQqKp74QRJxNmsQ231117==',
			 'ODEECiXLb/QyWUx6nbuYEMvA231118==',
			 'ODG3HCYbRfTV+3T5aTVT0O/A231118==',
			 'ODX/d2wDkfSHG63eNayWCR4A231118==',
			 'ODMNhkCtCFR+uvyVj07gA92g231118==',
			 'ODBm89q4znSaKSC7Hcht+ZOw231118==',
			 'ODSN7uVTaZSE2NWdsj2nwLog231118==',
			 'ODVJlRwV7jRxGXdUMgdiSfUg231118==',
			 'ODalrk2MNKRdufN6wVz+8MGQ231118==',
			 'ODkPs4rac3RIyAxMOxmADstw231118==',
			 'OD6+hykydCSx++LVu/sxRLkQ231118==',
			 'ODK4vAmuLWSH2k85DXDX/weA231118==',
			 'ODQgC5yvRRRLiFVe45s61kkg231118==',
			 'OD/vvgq/OhQ1KEsOr6TpaD8A231118==',
			 'ODmnjIyyxAQEKQKWOkk6CPzA231118==',
			 'ODL0DQBXPYRaWIlebZUGUg/Q231118==',
			 'ODFk29mRBNSO2Tv/KOzcOsSw231118==',
			 'ODS8EvSyukS9CuGAF8Ci3TPA231118==',
			 'ODjj8UFAO1TpSCTGJUz4EAiA231118==',
			 'OD1o3nxrzCSa2GDbV4idmE0A231118==')
  AND status in ('MANUAL_INTERVENTION','IN_FULFILLMENT') and WORKFLOW in ('CREATE_DEPOSIT','PRECLOSE_DEPOSIT');



UPDATE deposit_requests SET state = 'REQUEST_FAILED', last_attempt = true, updated_at = NOW()
where id in ('DRTFFhbxTrQx655OEagsXlBg231115==',
			 'DRkxGC9gONQROcsptv1ZIiLg231117==',
			 'DR0f5Nx+PoQWmPBDpAB4MWsA231117==',
			 'DRjqUR32CTTOeUx7+hsNVPpw231117==',
			 'DR8WQ8JdZVTlmLJvI1PXfxyQ231117==',
			 'DRE0rJzf/QQly8wu1O2VBDQA231117==',
			 'DRAIDM4OQiTs22cSgzsIdsqg231117==',
			 'DRqGGW2Y3YS1azIlbTQUVP9w231117==',
			 'DRFMcRN6/yT9ikdBbX1bWAgg231117==',
			 'DR4KPmCgbvTPe7ajJqjp6DzA231117==',
			 'DReBq+4Q82S7aNnYe4XvAL1Q231117==',
			 'DR0L6yMsJvRgScgvkSAgJ0Wg231117==',
			 'DRt38tMHXNQxeKe5ljml5czA231117==',
			 'DR2R+pTIDoTwWt/jqU6uoUTQ231117==',
			 'DREseccilwSHm8EyZlAho4ew231118==',
			 'DRZpCQNAeFTISMM/uscRVnIg231118==',
			 'DROE9/8JPfSzSbN3x9l9GqXA231118==',
			 'DRcKL6Av/IScmv27gTRdCCNQ231118==',
			 'DRTj4nYYj9T+2nEmfiExWFMg231118==',
			 'DROP0OaCpRQx28tSNupmJCGw231118==',
			 'DROCs9KsekTV+7/fXqI3WM6Q231118==',
			 'DRp+szOzScS16IPTF6xd8tWg231118==',
			 'DRwwvZ8BaBQx2oGA8LKbDoqg231118==',
			 'DRmAWCCud8SPq3HXrwferJzg231118==',
			 'DRboyzvC6kTsqBDKo2YhK18w231118==',
			 'DRWvMkRyJOT9KQW+Oei+bY5Q231118==',
			 'DRc4EA70NXT9+k43eAcFs/bg231118==',
			 'DRQHb6BH4XRPi0P3tMJfu4pg231118==',
			 'DRDDhbT6KqTkWAyo/OlmqQ3w231118==',
			 'DRzKMFdXfmSXW+/0DomhayAQ231118==',
			 'DRrXaunlFvT7ywxR2F0cNTww231118==',
			 'DRhFOac7/lTVmm6o5qArAvRA231118==',
			 'DRl7TEG6GoTumSBW3YTmrMBg231118==')
  and state in ('REQUEST_IN_PROGRESS','REQUEST_UNKNOWN');
