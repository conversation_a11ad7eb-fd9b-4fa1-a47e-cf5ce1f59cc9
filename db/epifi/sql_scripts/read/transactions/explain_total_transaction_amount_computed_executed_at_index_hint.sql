-- Same as query in explain_total_transaction_amount_computed_executed_at.sql with index hint to use pi_from_computed_executed_at_status index.
EXPLAIN ANALYZE SELECT sum(computed_amount) FROM transactions@pi_from_computed_executed_at_status_stored_idx
WHERE (pi_from in ('PINa1urqq7Q7mH/RmPo/y11Q230929==','PI210623Ht+p4N+NRQ+NqK5c5ccTzw==','PI210623nAVe69rlQeiOIUpkvETMDA=='))
AND status IN ('SUCCESS','UNKNOWN','MANUAL_INTERVENTION','IN_PROGRESS','INITIATED')
AND (computed_executed_at >= '2025-01-01 00:00:00' ::TIMESTAMPTZ)
AND created_at >= '2025-01-01 00:00:00'
AND ((computed_executed_at <= '2025-01-03 00:00:00' ::TIMESTAMPTZ)
OR created_at <= '2025-01-03 00:00:00')
AND payment_protocol IN ('INTRA_BANK','NEFT','IMPS','RTGS','UPI','CARD','AEPS','SWIFT','ENACH','CTS');
