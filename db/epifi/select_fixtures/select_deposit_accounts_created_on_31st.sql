-- get a list of all deposit accounts which were created on 31st of a month.
-- We are also fetching deposit accounts created one day before and after 31st for sanity.
select da.id,
       da.actor_id,
       da.account_number,
       u.fed_customer_info -> 'Id' as customer_id,
       da.name,
       da.maturity_date,
       da.state,
       da.term,
       da.interest_rate,
       da.operative_account_number,
       da.provenance,
       da.created_at,
       a.entity_id                 as user_id
from deposit_accounts as da
         INNER JOIN actors as a on da.actor_id = a.id
         INNER JOIN users as u on a.entity_id = u.id
where da.type = 'SMART_DEPOSIT'
  and (da.created_at between '2021-03-30'::date and '2021-04-01'::date
    or da.created_at between '2021-05-30'::date and '2021-06-01'::date)
order by da.provenance, da.created_at;
