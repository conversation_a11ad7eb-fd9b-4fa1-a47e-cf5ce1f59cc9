-- https://monorail.pointz.in/p/fi-app/issues/detail?id=89979
update auth_factor_updates
set overall_status = 'OVERALL_STATUS_FAILED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = '0183b070-680a-4f41-a3de-c672e70d6f70';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_FAILED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = '71f7fcd0-b5dc-4beb-9df5-7b8f84ae4319';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_FAILED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = '7b70a4d0-c98f-41b2-80a8-2003e9ea30ce';

update auth_factor_updates
set overall_status = 'OVERALL_STATUS_FAILED',
	vendor_context = jsonb_set("vendor_context"::jsonb, '{state}', to_jsonb('REREGISTRATION_REQUESTED'))
where id = 'efde5590-cc95-4b9a-9ca6-c4c83acba653';
