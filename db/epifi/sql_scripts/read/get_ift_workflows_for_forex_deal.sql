select
	id,
	stage,
	status,
	encode(payload, 'escape')::json-> 'currencyExchangeDetails' -> 'beneficiaryAmount' ->> 'units' || '.' || coalesce (encode(payload, 'escape')::json-> 'currencyExchangeDetails' -> 'beneficiaryAmount' ->> 'nanos', '0') as amount,
	encode(payload, 'escape')::json -> 'currencyExchangeDetails' ->> 'exchangeRate' as exchange_rate,
	created_at,
	updated_at
from
	workflow_requests
where
		type = 'INTERNATIONAL_FUND_TRANSFER' and encode(payload, 'escape')::json -> 'currencyExchangeDetails' ->> 'forexRateId' = 'FXcbr5ZVjKS9S9mLn0fRXiFw240206==' order by updated_at desc;
