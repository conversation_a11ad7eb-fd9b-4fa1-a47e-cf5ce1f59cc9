-- Removing BKYC whitelisting for internal user for testing Whitelisting flow.
DELETE
FROM user_group_mappings
WHERE user_group = 'BKYC'
  AND identifier_type = 'IDENTIFIER_TYPE_PHONE_NUMBER'
  AND identifier_value =
	  (SELECT computed_phone_number as identifier_value from users where id = 'ea3014ab-8362-4bdd-ba4c-079db0d4ff47');

DELETE
FROM user_group_mappings
WHERE user_group = 'BKYC'
  AND identifier_type = 'IDENTIFIER_TYPE_EMAIL'
  AND identifier_value =
	  (SELECT computed_email as identifier_value from users where id = 'ea3014ab-8362-4bdd-ba4c-079db0d4ff47');
