--order details in https://docs.google.com/spreadsheets/d/1ZpAa71T6NyZj8QVXGKFKEju_3JXhIoRbmrdqrcjqIAg/edit?usp=sharing
--updating the deposit request state and respective oms order state to failed as all these requests are initiated during CSIS
--time and stuck.
UPDATE orders SET status = 'FULFILLMENT_FAILED', updated_at = NOW()
where id in ('ODWI8yri9uQLWjXGe+8Sw4HA231108==',
			 'ODfqn/7Mz4QXq79Wj2TyUFFw231109==',
			 'ODB/UA4fqbTqmQy2ee51KfkQ231110==',
			 'OD/13LyFWySsKwamaJQPH5dQ231110==',
			 'ODuAK+BuB1RVCEKfGcBY1CkQ231111==',
			 'ODOmy6n9EjQMCYYnuEcY4lbQ231111==',
			 'ODFot4l7gkTcWUW18WSHLoHw231112==',
			 'ODT6VO7OdBQkmMHw8J1WyfkQ231112==',
			 'ODcvBGoEYzT8OrgpuZOZCYTw231112==',
			 'ODLDkc+6wIQwednZDyYAqn1w231113==',
			 'ODVdV80F4zSu6x7wMl0n6JOA231113==',
			 'ODUOYK47abSGSasMonjHx5Cw231113==',
			 'ODI+0IccWORCy2u77RdsDtpw231113==',
			 'ODL4R51pAhRJGWRBFoylQ4aQ231114==',
			 'ODhZNV3J2MThGu1a+Dkj46Mw231114==',
			 'OD5cn0+muHTs2TdYUoLsZslQ231114==',
			 'ODxoFg9uZwTY+fvUO5Je0l0A231114==')
AND status in ('MANUAL_INTERVENTION','IN_FULFILLMENT') and WORKFLOW in ('CREATE_DEPOSIT','PRECLOSE_DEPOSIT');



UPDATE deposit_requests SET state = 'REQUEST_FAILED', last_attempt = true, updated_at = NOW()
where id in ('DR8KwC9Fb3S261XgXJnsiF2A231107==',
			 'DRpkVoC2fLTemLtNVmYbCORg231108==',
			 'DRHcaOzZaDQ8e9pSmpJ0pztA231109==',
			 'DRw1J3BWH6T5ONXy3WrmK+Xw231109==',
			 'DR5ioYprR4S62IYF3WZgrOqw231109==',
			 'DRy9iN4e2RQoWKrQKXCZ4rvQ231110==',
			 'DR2rX7Iy6pRki2XyBOouNEwA231110==',
			 'DR0u4NP5LkTO2LmnEY4i0i9Q231111==',
			 'DRCnVZgaxUSGaKmKwG1IHhCw231111==',
			 'DR8cs/MjTsSfavrrL5zM77Ow231112==',
			 'DRmXeKms9PSu2Sg4vuASKMUw231112==',
			 'DRymDGrxIDRZG0sgdXISZl1Q231112==',
			 'DRCPSTfliaTtSgbPuDCRqtLQ231113==',
			 'DR1AtCaVBKQ76s8QiKyfVthw231113==',
			 'DRkhnsT+6ZQMy3I81773BmmA231113==',
			 'DROgNEE4qMTPaohJYKFROKLw231113==',
			 'DRQcu4T4P9QEyXqEPSZdbwpQ231114==',
			 'DRNQuvS73mTVqAQk/ZO7q6Ug231114==',
			 'DRunExmexTSB6kbllTePp1zg231114==',
			 'DRBh7YMgmXSHuOsQc1q0W1Ow231114==')
  and state in ('REQUEST_IN_PROGRESS','REQUEST_UNKNOWN');
