-- Reset the status of inward TTUM FGA to allow file to be recreated.
UPDATE foreign_remittance_file_generation_attempts
SET file_status = 'FILE_STATUS_CREATION_INITIATED'
WHERE id = '2a9fafbd-458d-4f51-9dda-609c312918d8';
-- i.e. client req ID: USSRP3Fe32ap89S240402

-- Delete entities mapped to the inward TTUM FGA
UPDATE ift_file_entity_mappings
SET deleted_at_unix = extract(epoch from now())
WHERE fga_attempt_id = '2a9fafbd-458d-4f51-9dda-609c312918d8';

-- Delete the corresponding GST reporting FGA
DELETE FROM foreign_remittance_file_generation_attempts
WHERE id = '6c6cc2fb-5b8f-4ebf-a71d-7eddd63f5ec6';
-- i.e. client req ID: USSRP3Fe32ap89S240402_GST
